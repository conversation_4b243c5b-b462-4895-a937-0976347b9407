/**
 * DemoControl with Text mixin
 */
ig.module('game.entities.demo-control')
.requires(
    'impact.entity',
    'plugins.utils.buttons.button-base',
    'plugins.utils.objects.popup',
    'plugins.utils.text'
)
.defines(function () {
    EntityDemoControl = ig.Entity.extend({
        zIndex: 3,

        arrow: new ig.Image('media/graphics/sprites/arrow.png'),
        arrow2: new ig.Image('media/graphics/sprites/arrow2.png'),

        // Text configuration for sample text
        entityTextConfig: {
            fontSize: 90,
            fontFamily: 'Arial',
            fontColor: '#000000',
            text: "Lorem\nipsum",
            align: 'center',
            vAlign: 'middle',
            width: 600,
            height: 200,
            debug: true
        },

        // Position for the sample text
        entityTextOffset: {
            x: 100,
            y: 200
        },

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            /* GAME STAGE */
            this.gameStage = {
                x: 0,
                y: 0,
                w: ig.sizeHandler.minW,
                h: ig.sizeHandler.minH,
                w2: ig.sizeHandler.minW / 2,
                h2: ig.sizeHandler.minH / 2,
                wText: ig.sizeHandler.minW + ' px (ig.sizeHandler.minW)',
                hText: ig.sizeHandler.minH + ' px (ig.sizeHandler.minH)'
            };

            this.repos();

            ig.game.spawnEntity(EntityInGame, ig.sizeHandler.minW - 267 - 15, 15);
            ig.game.spawnEntity(EntityUI, 0, 0); // pos is handled in repos()
            ig.game.spawnEntity(EntityCoverBackground, 0, 0);

            ig.game.spawnEntity(ig.FullscreenButton, 0, 0, {
                anchor: 2 // 1: top-left, 2: top-right, 3: bottom-right, 4: bottom-left
            });

            ig.game.spawnEntity(EntityButtonMoreGames, 100, 600);

            window.btn = ig.game.spawnEntity(EntityButtonText, 100, 200, {
                textConfig: { text: 'Spawn Popup' },
                onClickCallback: function () {
                    ig.game.spawnEntity(EntityPopupBase, 100, 100);
                }
            });
            ig.game.spawnEntity(EntityButtonSound, 100, 400);
            ig.game.spawnEntity(EntityButtonText, 100, 800, {
                textConfig: { text: 'Single Click Button' },
                singleClickOnly: true
            });

            // Initialize text capabilities
            this.initText(this.entityTextConfig);

            this.textOffset.x = ig.sizeHandler.minW * 0.5 - this.textConfig.width * 0.5;
            this.textOffset.y = ig.sizeHandler.minH * 0.5 - this.textConfig.height * 0.5;

            // Demo of the new convenience API:
            // 1) Auto-size the sample text to fit its configured box
            this.autoSizeCurrentText({ min: 24, max: 120, apply: true });

            // 2) Fade in the text
            this.fadeInText(300);

            // 3) Gently nudge text horizontally back and forth using tweenTextProps
            var baseOffset = { x: this.textOffset.x, y: this.textOffset.y };
            var wiggle = this.tweenTextProps({ textOffset: { x: baseOffset.x + 15, y: baseOffset.y } }, 600);
            if (wiggle && typeof wiggle.yoyo === 'function') { wiggle.yoyo(true).repeat(Infinity); }

            ig.game.sortEntitiesDeferred();
        },

        draw: function () {
            ig.drawUtil.strokeStyle('#F00', 8);
            ig.drawUtil.fontStyle('30px Arial', '#1a941a', 'left');

            var c = ig.system.context;
            c.save();

            /* SINCE THE POSITIONS ARE RELATIVE TO THE GAME ORIGIN
               HENCE, TRANSLATE TO THE GAME STAGE ORIGIN */
            c.translate(-ig.game.screen.x, -ig.game.screen.y);

            c.strokeRect(this.gameStage.x, this.gameStage.y, this.gameStage.w, this.gameStage.h);

            this.arrow.draw(5, 5);
            c.fillText('Game origin (0, 0)', 80, 104);

            c.textAlign = 'center';
            c.fillStyle = this.systemWidthTextColor;
            c.fillText(this.systemWidthText, this.gameStage.w2, this.gameStage.h2);
            c.fillStyle = this.systemHeightTextColor;
            c.fillText(this.systemHeightText, this.gameStage.w2, this.gameStage.h2 + 50);

            c.fillStyle = 'red';
            c.fillText(this.gameStage.wText, this.gameStage.w2, this.gameStage.h - 15);
            c.translate(0, this.gameStage.h2);
            c.rotate(-ig.utils.pio2);
            c.fillText(this.gameStage.hText, 0, 35);

            c.restore();

            /* BELOW ARE RELATIVE TO THE SCREEN ORIGIN */
            this.arrow2.draw(5, 5);

            // Draw sample text using the mixin
            this.drawText();

            c.fillStyle = '#781897';
            c.fillText(this.cameraPosText, 85, 75);
        },

        repos: function () {
            this.cameraPosText = 'SCREEN (CAMERA) position (' + ig.game.screen.x + ', ' + ig.game.screen.y + ')';
            this.systemWidthText = 'ig.system.width = ' + ig.system.width + ' px';
            this.systemHeightText = 'ig.system.height = ' + ig.system.height + ' px';
            this.systemWidthTextColor = ig.system.width > ig.sizeHandler.minW ? 'purple' : 'blue';
            this.systemHeightTextColor = ig.system.height > ig.sizeHandler.minH ? 'purple' : 'blue';

            this.gameStage = {
                x: 0,
                y: 0,
                w: ig.sizeHandler.minW,
                h: ig.sizeHandler.minH,
                w2: ig.sizeHandler.minW / 2,
                h2: ig.sizeHandler.minH / 2,
                wText: ig.sizeHandler.minW + ' px (ig.sizeHandler.minW)',
                hText: ig.sizeHandler.minH + ' px (ig.sizeHandler.minH)'
            };
        },

        // Split text convenience method
        splitText: function (text, options) {
            return ig.textToCanvas.splitText({
                ctx: ig.system.context,
                text: text,
                width: options.width || this.entityTextConfig.width,
                height: options.height || this.entityTextConfig.height,
                fontSize: options.fontSize || this.entityTextConfig.fontSize,
                fontFamily: options.fontFamily || this.entityTextConfig.fontFamily
            });
        }
    });

    // Extend with consolidated Text mixin
    if (ig.TextLabelMixin) {
        EntityDemoControl.inject(ig.TextLabelMixin);
    } else {
        console.error('TextLabelMixin not found; ensure plugins.utils.text is required.');
    }

    EntityInGame = ig.Entity.extend({
        zIndex: 2,
        image: new ig.Image('media/graphics/sprites/ingame-entity.png'),
        isClickable: true,
        size: {
            x: 267,
            y: 108
        },

        draw: function () {
            /*  ITS POSITION IS RELATIVE TO THE GAME ORIGIN
                HENCE, SUBTRACT IT WITH THE SCREEN POSITION
            */
            this.image.draw(this.pos.x - ig.game.screen.x, this.pos.y - ig.game.screen.y);
        },

        clicked: function () {
            console.log('EntityInGame is clicked.');
        }
    });

    EntityUI = ig.Entity.extend({
        zIndex: 5,
        image: new ig.Image('media/graphics/sprites/ui-entity.png'),
        isClickable: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.size.x = this.image.width;
            this.size.y = this.image.height;
            this.repos();
        },

        draw: function () {
            /*  ITS POSITION IS RELATIVE TO THE SCREEN ORIGIN
                HENCE, NO NEED TO SUBTRACT IT WITH THE SCREEN POSITION
            */
            this.image.draw(this.pos.x, this.pos.y);
        },

        repos: function () {
            this.pos.x = ig.system.width - this.image.width - 15;
            this.pos.y = ig.system.height - this.image.height - 15;
        },

        clicked: function () {
            console.log('EntityUI is clicked.');
        },

        /* OVERRIDE DEFAULT METHOD */
        underPointer: function () {
            var p = ig.game.io.getClickPos();
            return this.containPoint(p);
        }
    });

    /* ---------- DRAW A BACKGROUND THAT COVER THE WHOLE SCREEN WITHOUT STRETCHING ---------- */
    EntityCoverBackground = ig.Entity.extend({
        zIndex: 1,
        image: new ig.Image('media/graphics/sprites/background.png'),

        init: function (x, y, settings) {
            this.repos();
        },

        draw: function () {
            ig.system.context.drawImage(this.image.data, this.bgX, this.bgY, this.bgW, this.bgH, 0, 0, ig.system.width, ig.system.height);
        },

        repos: function () {
            var r1 = this.image.width / this.image.height;
                var r2 = ig.system.width / ig.system.height;
            if (r1 > r2) {
                this.bgH = this.image.height;
                this.bgW = this.bgH * r2;
                this.bgX = (this.image.width - this.bgW) / 2;
                this.bgY = 0;
            } else {
                this.bgW = this.image.width;
                this.bgH = this.bgW / r2;
                this.bgX = 0;
                this.bgY = (this.image.height - this.bgH) / 2;
            }
        }
    });
});
