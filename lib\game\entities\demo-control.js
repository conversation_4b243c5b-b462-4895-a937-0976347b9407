/**
 * DemoControl with Text mixin
 */
ig.module('game.entities.demo-control')
.requires(
    'impact.entity',
    'plugins.utils.buttons.button-base',
    'plugins.utils.objects.popup',
    'plugins.utils.text',
    'plugins.utils.docker.docker-plugin'
)
.defines(function () {
    EntityDemoControl = ig.Entity.extend({
        zIndex: 3,

        arrow: new ig.Image('media/graphics/sprites/arrow.png'),
        arrow2: new ig.Image('media/graphics/sprites/arrow2.png'),

        // Text configuration for sample text
        entityTextConfig: {
            fontSize: 90,
            fontFamily: 'Arial',
            fontColor: '#000000',
            text: "Lorem\nipsum",
            align: 'center',
            vAlign: 'middle',
            width: 600,
            height: 200,
            debug: true
        },

        // Position for the sample text
        entityTextOffset: {
            x: 100,
            y: 200
        },

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            /* GAME STAGE */
            this.gameStage = {
                x: 0,
                y: 0,
                w: ig.sizeHandler.minW,
                h: ig.sizeHandler.minH,
                w2: ig.sizeHandler.minW / 2,
                h2: ig.sizeHandler.minH / 2,
                wText: ig.sizeHandler.minW + ' px (ig.sizeHandler.minW)',
                hText: ig.sizeHandler.minH + ' px (ig.sizeHandler.minH)'
            };

            this.repos();

            ig.game.spawnEntity(EntityInGame, ig.sizeHandler.minW - 267 - 15, 15);
            ig.game.spawnEntity(EntityUI, 0, 0); // pos is handled in repos()
            ig.game.spawnEntity(EntityCoverBackground, 0, 0);

            ig.game.spawnEntity(ig.FullscreenButton, 0, 0, {
                anchor: 2 // 1: top-left, 2: top-right, 3: bottom-right, 4: bottom-left
            });

            ig.game.spawnEntity(EntityButtonMoreGames, 100, 600);

            window.btn = ig.game.spawnEntity(EntityButtonText, 100, 200, {
                textConfig: { text: 'Spawn Popup' },
                onClickCallback: function () {
                    ig.game.spawnEntity(EntityPopupBase, 100, 100);
                }
            });
            ig.game.spawnEntity(EntityButtonSound, 100, 400);
            ig.game.spawnEntity(EntityButtonText, 100, 800, {
                textConfig: { text: 'Single Click Button' },
                singleClickOnly: true
            });

            // Spawn Docker Demo Entity
            ig.game.spawnEntity(EntityDockerDemo, 0, 0);

            // Initialize text capabilities
            this.initText(this.entityTextConfig);

            this.textOffset.x = ig.sizeHandler.minW * 0.5 - this.textConfig.width * 0.5;
            this.textOffset.y = ig.sizeHandler.minH * 0.5 - this.textConfig.height * 0.5;

            // Demo of the new convenience API:
            // 1) Auto-size the sample text to fit its configured box
            this.autoSizeCurrentText({ min: 24, max: 120, apply: true });

            // 2) Fade in the text
            this.fadeInText(300);

            // 3) Gently nudge text horizontally back and forth using tweenTextProps
            var baseOffset = { x: this.textOffset.x, y: this.textOffset.y };
            var wiggle = this.tweenTextProps({ textOffset: { x: baseOffset.x + 15, y: baseOffset.y } }, 600);
            if (wiggle && typeof wiggle.yoyo === 'function') { wiggle.yoyo(true).repeat(Infinity); }

            ig.game.sortEntitiesDeferred();
        },

        draw: function () {
            ig.drawUtil.strokeStyle('#F00', 8);
            ig.drawUtil.fontStyle('30px Arial', '#1a941a', 'left');

            var c = ig.system.context;
            c.save();

            /* SINCE THE POSITIONS ARE RELATIVE TO THE GAME ORIGIN
               HENCE, TRANSLATE TO THE GAME STAGE ORIGIN */
            c.translate(-ig.game.screen.x, -ig.game.screen.y);

            c.strokeRect(this.gameStage.x, this.gameStage.y, this.gameStage.w, this.gameStage.h);

            this.arrow.draw(5, 5);
            c.fillText('Game origin (0, 0)', 80, 104);

            c.textAlign = 'center';
            c.fillStyle = this.systemWidthTextColor;
            c.fillText(this.systemWidthText, this.gameStage.w2, this.gameStage.h2);
            c.fillStyle = this.systemHeightTextColor;
            c.fillText(this.systemHeightText, this.gameStage.w2, this.gameStage.h2 + 50);

            c.fillStyle = 'red';
            c.fillText(this.gameStage.wText, this.gameStage.w2, this.gameStage.h - 15);
            c.translate(0, this.gameStage.h2);
            c.rotate(-ig.utils.pio2);
            c.fillText(this.gameStage.hText, 0, 35);

            c.restore();

            /* BELOW ARE RELATIVE TO THE SCREEN ORIGIN */
            this.arrow2.draw(5, 5);

            // Draw sample text using the mixin
            this.drawText();

            c.fillStyle = '#781897';
            c.fillText(this.cameraPosText, 85, 75);
        },

        repos: function () {
            this.cameraPosText = 'SCREEN (CAMERA) position (' + ig.game.screen.x + ', ' + ig.game.screen.y + ')';
            this.systemWidthText = 'ig.system.width = ' + ig.system.width + ' px';
            this.systemHeightText = 'ig.system.height = ' + ig.system.height + ' px';
            this.systemWidthTextColor = ig.system.width > ig.sizeHandler.minW ? 'purple' : 'blue';
            this.systemHeightTextColor = ig.system.height > ig.sizeHandler.minH ? 'purple' : 'blue';

            this.gameStage = {
                x: 0,
                y: 0,
                w: ig.sizeHandler.minW,
                h: ig.sizeHandler.minH,
                w2: ig.sizeHandler.minW / 2,
                h2: ig.sizeHandler.minH / 2,
                wText: ig.sizeHandler.minW + ' px (ig.sizeHandler.minW)',
                hText: ig.sizeHandler.minH + ' px (ig.sizeHandler.minH)'
            };
        },

        // Split text convenience method
        splitText: function (text, options) {
            return ig.textToCanvas.splitText({
                ctx: ig.system.context,
                text: text,
                width: options.width || this.entityTextConfig.width,
                height: options.height || this.entityTextConfig.height,
                fontSize: options.fontSize || this.entityTextConfig.fontSize,
                fontFamily: options.fontFamily || this.entityTextConfig.fontFamily
            });
        }
    });

    // Extend with consolidated Text mixin
    if (ig.TextLabelMixin) {
        EntityDemoControl.inject(ig.TextLabelMixin);
    } else {
        console.error('TextLabelMixin not found; ensure plugins.utils.text is required.');
    }

    EntityInGame = ig.Entity.extend({
        zIndex: 2,
        image: new ig.Image('media/graphics/sprites/ingame-entity.png'),
        isClickable: true,
        size: {
            x: 267,
            y: 108
        },

        draw: function () {
            /*  ITS POSITION IS RELATIVE TO THE GAME ORIGIN
                HENCE, SUBTRACT IT WITH THE SCREEN POSITION
            */
            this.image.draw(this.pos.x - ig.game.screen.x, this.pos.y - ig.game.screen.y);
        },

        clicked: function () {
            console.log('EntityInGame is clicked.');
        }
    });

    EntityUI = ig.Entity.extend({
        zIndex: 5,
        image: new ig.Image('media/graphics/sprites/ui-entity.png'),
        isClickable: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.size.x = this.image.width;
            this.size.y = this.image.height;
            this.repos();
        },

        draw: function () {
            /*  ITS POSITION IS RELATIVE TO THE SCREEN ORIGIN
                HENCE, NO NEED TO SUBTRACT IT WITH THE SCREEN POSITION
            */
            this.image.draw(this.pos.x, this.pos.y);
        },

        repos: function () {
            this.pos.x = ig.system.width - this.image.width - 15;
            this.pos.y = ig.system.height - this.image.height - 15;
        },

        clicked: function () {
            console.log('EntityUI is clicked.');
        },

        /* OVERRIDE DEFAULT METHOD */
        underPointer: function () {
            var p = ig.game.io.getClickPos();
            return this.containPoint(p);
        }
    });

    /* ---------- DRAW A BACKGROUND THAT COVER THE WHOLE SCREEN WITHOUT STRETCHING ---------- */
    EntityCoverBackground = ig.Entity.extend({
        zIndex: 1,
        image: new ig.Image('media/graphics/sprites/background.png'),

        init: function (x, y, settings) {
            this.repos();
        },

        draw: function () {
            ig.system.context.drawImage(this.image.data, this.bgX, this.bgY, this.bgW, this.bgH, 0, 0, ig.system.width, ig.system.height);
        },

        repos: function () {
            var r1 = this.image.width / this.image.height;
                var r2 = ig.system.width / ig.system.height;
            if (r1 > r2) {
                this.bgH = this.image.height;
                this.bgW = this.bgH * r2;
                this.bgX = (this.image.width - this.bgW) / 2;
                this.bgY = 0;
            } else {
                this.bgW = this.image.width;
                this.bgH = this.bgW / r2;
                this.bgX = 0;
                this.bgY = (this.image.height - this.bgH) / 2;
            }
        }
    });

    /* ---------- DOCKER DEMO ENTITY ---------- */
    EntityDockerDemo = ig.Entity.extend(ig.DockerMixin).extend({
        name: 'docker-demo',
        zIndex: 10,
        size: { x: 300, y: 200 },
        isClickable: true,

        // Demo panel configuration
        panelConfig: {
            backgroundColor: 'rgba(0, 50, 100, 0.8)',
            borderColor: '#00aaff',
            borderWidth: 2,
            cornerRadius: 8
        },

        // Docker demo states
        dockStates: [
            { name: 'Top Left', percent: { x: 0, y: 0 }, offset: { x: 10, y: 10 } },
            { name: 'Top Right', percent: { x: 1, y: 0 }, offset: { x: -310, y: 10 } },
            { name: 'Bottom Left', percent: { x: 0, y: 1 }, offset: { x: 10, y: -210 } },
            { name: 'Bottom Right', percent: { x: 1, y: 1 }, offset: { x: -310, y: -210 } },
            { name: 'Center', percent: { x: 0.5, y: 0.5 }, offset: { x: -150, y: -100 } }
        ],
        currentStateIndex: 0,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            console.log('Docker Demo: Starting initialization...');
            console.log('ig.game.screen:', ig.game.screen);
            console.log('ig.system:', { width: ig.system.width, height: ig.system.height });

            // Create a mock screen object for docking if needed
            if (!ig.game.screen.size) {
                ig.game.screen.size = {
                    x: ig.system.width,
                    y: ig.system.height
                };
                console.log('Docker Demo: Created screen.size:', ig.game.screen.size);
            }

            // Initialize docker component and dock to screen
            try {
                this.dockTo(ig.game.screen, this.dockStates[this.currentStateIndex]);
                console.log('Docker Demo: Successfully docked to screen');
            } catch (error) {
                console.error('Docker Demo: Error during docking:', error);
            }

            // Create child elements
            this.createDemoElements();

            console.log('Docker Demo Panel initialized - Click to cycle through dock positions');
        },

        createDemoElements: function () {
            // Create a toggle button for docker states
            this.toggleButton = ig.game.spawnEntity(EntityButtonText, 0, 0, {
                textConfig: {
                    text: 'Docker: ' + this.dockStates[this.currentStateIndex].name,
                    fontSize: 16,
                    fontColor: '#ffffff'
                },
                size: { x: 200, y: 30 },
                zIndex: this.zIndex + 1,
                onClickCallback: this.cycleDockState.bind(this)
            });

            // Create an info display button
            this.infoButton = ig.game.spawnEntity(EntityButtonText, 0, 0, {
                textConfig: {
                    text: 'Docker Component Demo',
                    fontSize: 14,
                    fontColor: '#aaffff'
                },
                size: { x: 280, y: 25 },
                zIndex: this.zIndex + 1,
                onClickCallback: this.showDockerInfo.bind(this)
            });

            // Position child elements relative to this panel
            this.updateChildPositions();
        },

        updateChildPositions: function () {
            if (this.toggleButton) {
                this.toggleButton.pos.x = this.pos.x + 50;
                this.toggleButton.pos.y = this.pos.y + 50;
            }

            if (this.infoButton) {
                this.infoButton.pos.x = this.pos.x + 10;
                this.infoButton.pos.y = this.pos.y + 10;
            }
        },

        cycleDockState: function () {
            // Cycle to next dock state
            this.currentStateIndex = (this.currentStateIndex + 1) % this.dockStates.length;
            var newState = this.dockStates[this.currentStateIndex];

            // Update docker configuration
            this.updateDockerConfig({
                dockerObject: ig.game.screen,
                dockerPercent: newState.percent,
                dockerOffset: newState.offset
            });

            // Update button text
            if (this.toggleButton && this.toggleButton.updateText) {
                this.toggleButton.updateText('Docker: ' + newState.name);
            }

            console.log('Docker Demo: Switched to ' + newState.name + ' position');
        },

        showDockerInfo: function () {
            var config = this.getDockerConfig();
            console.log('Docker Configuration:', {
                dockerObject: config.dockerObject === ig.game.screen ? 'ig.game.screen' : 'other',
                percent: config.dockerPercent,
                offset: config.dockerOffset,
                enabled: config.enabled,
                screenPos: { x: ig.game.screen.x, y: ig.game.screen.y },
                screenSize: ig.game.screen.size
            });
        },

        update: function () {
            this.parent();

            // Update child element positions when this entity moves
            this.updateChildPositions();
        },

        draw: function () {
            var ctx = ig.system.context;
            ctx.save();

            // Draw panel background
            ctx.fillStyle = this.panelConfig.backgroundColor;
            ctx.strokeStyle = this.panelConfig.borderColor;
            ctx.lineWidth = this.panelConfig.borderWidth;

            // Draw rounded rectangle
            this.drawRoundedRect(
                ctx,
                this.pos.x,
                this.pos.y,
                this.size.x,
                this.size.y,
                this.panelConfig.cornerRadius
            );

            ctx.fill();
            ctx.stroke();

            // Draw title text
            ctx.fillStyle = '#ffffff';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(
                'Docker Demo Panel',
                this.pos.x + this.size.x / 2,
                this.pos.y + 30
            );

            // Draw current state info
            ctx.font = '12px Arial';
            ctx.fillStyle = '#aaffff';
            var state = this.dockStates[this.currentStateIndex];
            ctx.fillText(
                'Position: ' + JSON.stringify(state.percent),
                this.pos.x + this.size.x / 2,
                this.pos.y + 120
            );
            ctx.fillText(
                'Offset: ' + JSON.stringify(state.offset),
                this.pos.x + this.size.x / 2,
                this.pos.y + 140
            );

            // Draw docker status
            var dockerEnabled = this.dockerComponent && this.dockerComponent.enable;
            ctx.fillStyle = dockerEnabled ? '#00ff00' : '#ff0000';
            ctx.fillText(
                'Docker: ' + (dockerEnabled ? 'ENABLED' : 'DISABLED'),
                this.pos.x + this.size.x / 2,
                this.pos.y + 170
            );

            ctx.restore();
        },

        drawRoundedRect: function (ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        },

        clicked: function () {
            this.cycleDockState();
        },

        kill: function () {
            // Clean up child elements
            if (this.toggleButton) {
                this.toggleButton.kill();
            }
            if (this.infoButton) {
                this.infoButton.kill();
            }
            this.parent();
        }
    });
});
