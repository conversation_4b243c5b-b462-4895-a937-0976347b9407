/**
 * Responsive Plugin by Nam
 */
ig.module('plugins.responsive')
.requires(
    'plugins.handlers.size-handler'
)
.defines(function () {

    ig.SizeHandler.inject({
        RESOLUTION: {
            LANDSCAPE: { width: 1600, height: 900 },
            PORTRAIT: { width: 900, height: 1600 }
        },
        firstResize: true,
        isPortrait: false,

        sizeCalcs: function () {
            var w = window.innerWidth,
                h = window.innerHeight,
                newW, newH;

            if (h > w) {
                // Portrait mode
                this.isPortrait = true;
                this.minW = this.RESOLUTION.PORTRAIT.width;
                this.minH = this.RESOLUTION.PORTRAIT.height;
            } else {
                // Landscape mode
                this.isPortrait = false;
                this.minW = this.RESOLUTION.LANDSCAPE.width;
                this.minH = this.RESOLUTION.LANDSCAPE.height;
            }

            this.r0 = this.minW / this.minH;
            var r = w / h;

            // Calculate new game size
            if (r < this.r0) {
                newW = this.minW;
                newH = Math.round(newW / r);
            } else {
                newH = this.minH;
                newW = Math.round(newH * r);
            }

            if (ig.system) {
                this.dx = (ig.system.width - newW) / 2;
                this.dy = (ig.system.height - newH) / 2;
                ig.system.resize(newW, newH, this.scale);
            }

            this.windowSize = new Vector2(w, h);
            this.scaleRatioMultiplier = new Vector2(w / newW, h / newH);
            this.desktop.actualResolution = new Vector2(w, h);
            this.mobile.actualResolution = new Vector2(w, h);
            this.desktop.actualSize = new Vector2(w, h);
            this.mobile.actualSize = new Vector2(w, h);

            if (ig.game) {
                ig.game.midX = newW / 2;
                ig.game.midY = newH / 2;

                var dx2 = (this.minW - newW) / 2;
                var dy2 = (this.minH - newH) / 2;

                // Adjust the game screen based on initial offsets
                ig.game.screen.x = dx2;
                ig.game.screen.y = dy2;

                ig.game.update();

                // Reposition entities
                this.repositionEntities();

                // Update drawing
                ig.game.draw();

            } else if (ig.loader) {
                ig.loader.draw(); // update splash-loader screen
            }
        },

        // Reposition entities
        repositionEntities: function () {
            ig.game.entities.forEach(function (e) {
                e && typeof e.repos === 'function' && e.repos();
            });
        },

        // Override method to get rid of the orientation image
        reorient: function () {
            this.resize();
            ig.ua.mobile && this.resizeAds();
            if (ig.game) ig.game.initGameStage();
        }
    });
});
