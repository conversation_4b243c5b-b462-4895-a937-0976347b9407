ig.module('game.entities.controllers.settings-controller')
.requires(
	'impact.entity',
    'plugins.utils.entity-extended',
    'plugins.utils.buttons.button-image'
)
.defines(function () {
    EntityButtonSettings = EntityButtonImage.extend({
        name: 'button-settings',
        zIndex: 10,
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/buttons/button-settings.png'), frameCountX: 1, frameCountY: 1 },
        hasText: false,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
        },
        onClickCallback: function () {
            this.controller.toggleButtonsClickable(false);
            var popup = ig.game.spawnEntity(EntitySettingsPopup, -999, -999, {
                zIndex: this.zIndex + 10,
                btnSettings: this
            });
            popup.pos.x = ig.game.gameStage.hW - popup.idleSheetInfo.sheetImage.width * 0.5;
            popup.pos.y = ig.system.height + popup.idleSheetInfo.sheetImage.height + 100;
            popup.show();
        }
    });

    EntitySettingsPopup = EntityMarketjsEntity.extend({
        name: 'popup-settings',
        zIndex: 10,
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/popup-stats.png'), frameCountX: 1, frameCountY: 1 },
        onShowCallback: null,
        onCloseCallback: null,
        bgScale: 0,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            window.settingsPopup = this;
            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.idle;
            this.setupButtons();
            ig.game.sortEntitiesDeferred();
        },

        setupButtons: function () {
            this.btnSettingsHome = ig.game.spawnEntity(EntityButtonSettingsHome, this.pos.x, -999, { zIndex: this.zIndex + 1, controller: this });
            this.btnSettingsResume = ig.game.spawnEntity(EntityButtonSettingsResume, this.pos.x, -999, { zIndex: this.zIndex + 1, controller: this });
        },

        show: function () {
            var target = { y: ig.game.gameStage.hH - this.idleSheetInfo.sheetImage.height * 0.5 };
            var tween = new ig.TweenDef(this.pos)
            .easing(ig.Tween.Easing.Back.EaseOut)
            .to({ y: target.y }, 300)
            .onUpdate(function () {
                this.anchorButtons();
            }.bind(this))
            .onComplete(function () {
                if (this.onShowCallback) this.onShowCallback();
            }.bind(this))
            .start();
            ig.game.tweens.add(tween);
        },

        hide: function () {
            var target = { y: -(this.size.y + 100) };
            var tween = new ig.TweenDef(this.pos)
            .easing(ig.Tween.Easing.Back.EaseIn)
            .to({ y: target.y }, 300)
            .onUpdate(function () {
            }.bind(this))
            .onComplete(function () {
                if (this.onCloseCallback) this.onCloseCallback();
                this.btnSettings.controller.toggleButtonsClickable(true);
                this.kill();
            }.bind(this))
            .start();
            ig.game.tweens.add(tween);
        },

        anchorButtons: function () {
            this.btnSettingsHome.pos.x = this.pos.x - this.btnSettingsHome.idleSheetInfo.sheetImage.width * 0.5;
            this.btnSettingsHome.pos.y = this.pos.y + 50;
            this.btnSettingsResume.pos.x = this.pos.x - this.btnSettingsResume.idleSheetInfo.sheetImage.width * 0.5;
            this.btnSettingsResume.pos.y = this.pos.y + 50 + this.btnSettingsHome.idleSheetInfo.sheetImage.height + 10;
        },

        draw: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(0, 0, ig.system.width, ig.system.height);
            ctx.restore();
            this.parent();
        }
    });

    EntityButtonSettingsHome = EntityButtonImage.extend({
        name: 'button-settings',
        zIndex: 10,
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/buttons/button-home.png'), frameCountX: 1, frameCountY: 1 },
        hasText: false,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
        },
        onClickCallback: function () {
        }
    });

    EntityButtonSettingsResume = EntityButtonImage.extend({
        name: 'button-settings',
        zIndex: 10,
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/buttons/button-resume.png'), frameCountX: 1, frameCountY: 1 },
        hasText: false,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
        },
        onClickCallback: function () {
        }
    });
});
