ig.module(
    'plugins.utils.docker.docker-mixin'
).requires(
    'plugins.utils.docker.docker-component'
).defines(function () {
    'use strict';

    /**
     * Docker Mixin Plugin
     * Provides easy integration of Docker functionality into entities
     *
     * Usage:
     *   ig.Entity.extend(ig.DockerMixin).extend({
     *       // your entity implementation
     *   });
     *
     * The mixin automatically:
     * - Adds Docker component during initComponents
     * - Provides helper methods for common docker operations
     * - Maintains backward compatibility with existing docker usage
     */

    ig.DockerMixin = {
        /**
         * Initialize Docker component during entity initialization
         */
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this._initDockerComponent();
        },

        /**
         * Initialize Docker component with default settings
         * Can be overridden in child classes for custom configuration
         */
        _initDockerComponent: function () {
            // Only initialize if not already present
            if (!this.dockerComponent) {
                this.dockerComponent = new ig.DockerComponent(this);

                // Ensure screen has size property for docking
                if (ig.game.screen && !ig.game.screen.size) {
                    ig.game.screen.size = {
                        x: ig.system.width,
                        y: ig.system.height
                    };
                }

                this.dockerComponent.initWithSettings({
                    dockerObject: ig.game.screen || ig.game.camera || (typeof dl !== 'undefined' ? dl.game.camera : null),
                    dockerPercent: { x: 0.5, y: 0.5 },  // Center
                    dockerOffset: { x: 0, y: 0 }   // No offset
                });
            }
        },

        /**
         * Dock this entity to another object
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} options - Optional configuration
         * @param {Object} options.percent - Docker percentage {x, y} (default: {x: 0.5, y: 0.5})
         * @param {Object} options.offset - Docker offset {x, y} (default: {x: 0, y: 0})
         */
        dockTo: function (dockerObject, options) {
            if (!this.dockerComponent) {
                this._initDockerComponent();
            }
            this.dockerComponent.setDockerObject(dockerObject, options);
        },

        /**
         * Dock this entity to the camera/screen
         * @param {Object} options - Optional configuration
         * @param {Object} options.percent - Docker percentage {x, y} (default: {x: 0.5, y: 0.5})
         * @param {Object} options.offset - Docker offset {x, y} (default: {x: 0, y: 0})
         */
        dockToCamera: function (options) {
            if (!this.dockerComponent) {
                this._initDockerComponent();
            }

            // Ensure screen has size property
            if (ig.game.screen && !ig.game.screen.size) {
                ig.game.screen.size = {
                    x: ig.system.width,
                    y: ig.system.height
                };
            }

            // Use screen if available, otherwise fall back to camera
            var target = ig.game.screen || ig.game.camera || (typeof dl !== 'undefined' ? dl.game.camera : null);
            this.dockerComponent.setDockerObject(target, options);
        },

        /**
         * Update docker positioning configuration
         * @param {Object} config - Configuration object
         * @param {Object} config.dockerObject - Object to dock to
         * @param {Object} config.dockerPercent - Docker percentage {x, y}
         * @param {Object} config.dockerOffset - Docker offset {x, y}
         */
        updateDockerConfig: function (config) {
            if (!this.dockerComponent) {
                this._initDockerComponent();
            }
            this.dockerComponent.updateProperties(config);
        },

        /**
         * Enable or disable docker positioning
         * @param {boolean} enabled - Whether docker positioning should be enabled
         */
        setDockerEnabled: function (enabled) {
            if (!this.dockerComponent) {
                this._initDockerComponent();
            }
            this.dockerComponent.enable = enabled;
        },

        /**
         * Get current docker configuration
         * @returns {Object} Current docker configuration
         */
        getDockerConfig: function () {
            if (!this.dockerComponent) {
                return null;
            }
            return {
                dockerObject: this.dockerComponent.dockerObject,
                dockerPercent: this.dockerComponent.dockerPercent,
                dockerOffset: this.dockerComponent.dockerOffset,
                enabled: this.dockerComponent.enable
            };
        },

        /**
         * Dock to top-left corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToTopLeft: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 0, y: 0 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to top-right corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToTopRight: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 1, y: 0 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to bottom-left corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToBottomLeft: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 0, y: 1 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to bottom-right corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToBottomRight: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 1, y: 1 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to center of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToCenter: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 0.5, y: 0.5 },
                offset: offset || { x: 0, y: 0 }
            });
        }
    };
});
