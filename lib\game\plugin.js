/**
 * Plugin by Nam
 */
ig.module('game.plugin')
    .requires(
        'impact.image'
    ).defines(function () {
        ig.Image.inject({
            drawScale: function (x, y, w, h) {
                if (!this.loaded) {
                    return;
                }
                ig.system.context.drawImage(this.data, 0, 0, this.width, this.height, x, y, w, h);
                ig.Image.drawCount++;
            }
        });

        /* UTILES */
        ig.util = {
            pi2: Math.PI * 2,
            pio2: Math.PI / 2,
            lerp: function (value1, value2, amount) {
                amount = amount < 0 ? 0 : amount;
                amount = amount > 1 ? 1 : amount;
                return value1 + (value2 - value1) * amount;
            },
            clamp: function (val, min, max) {
                return Math.max(min, Math.min(max, val));
            },
            distanceBetweenPoints: function (p1, p2) {
                return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));
            },
            distanceBetween: function (x1, y1, x2, y2) {
                return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
            },
            angleBetweenPoints: function (p1, p2) {
                return Math.atan2(p2.y - p1.y, p2.x - p1.x);
            },
            angleBetween: function (x1, y1, x2, y2) {
                return Math.atan2(y2 - y1, x2 - x1);
            },
            toRad: function (deg) {
                return deg / 180 * Math.PI;
            },
            toDeg: function (rad) {
                return rad / Math.PI * 180;
            },
            rBetween: function (a, b) {
                return a + Math.random() * (b - a);
            },
            iBetween: function (a, b) {
                return a + Math.floor(Math.random() * (b - a + 1));
            },
            containPoint: function (vertices, point) {
                var check;
                for (var i = 0; i < vertices.length; i++) {
                    var j = i + 1;
                    if (j == vertices.length) j = 0;
                    var p0 = vertices[i];
                        var p1 = vertices[j];
                    var side = (point.y - p0.y) * (p1.x - p0.x) - (point.x - p0.x) * (p1.y - p0.y);
                    if (side == 0) return true;
                    if (i === 0) check = side;
                    else if (check * side < 0) return false;
                }
                return true;
            },
            pick: function (arr) {
                var r = Math.floor(Math.random() * arr.length);
                return arr.splice(r, 1)[0];
            },
            randomIn: function (arr) {
                var r = Math.floor(Math.random() * arr.length);
                return arr[r];
            }
        };

        ig.drawUtil = {
            fontStyle: function (font, color, align) {
                var c = ig.system.context;
                c.font = font;
                c.fillStyle = color;
                c.textAlign = align;
            },
            strokeStyle: function (color, lineWidth) {
                var c = ig.system.context;
                c.strokeStyle = color;
                c.lineWidth = lineWidth;
            },
            fillTextStroke: function (text, x, y) {
                var c = ig.system.context;
                c.strokeText(text, x, y);
                c.fillText(text, x, y);
            },
            clearScreen: function (color) {
                var c = ig.system.context;
                c.fillStyle = color;
                c.clearRect(0, 0, ig.system.width, ig.system.height);
            },
            fillScreen: function (color) {
                var c = ig.system.context;
                c.fillStyle = color;
                c.fillRect(0, 0, ig.system.width, ig.system.height);
            },
            roundRect: function (x, y, w, h, r) {
                var c = ig.system.context;
                c.beginPath();
                c.moveTo(x + r, y);
                c.arcTo(x + w, y, x + w, y + h, r);
                c.arcTo(x + w, y + h, x, y + h, r);
                c.arcTo(x, y + h, x, y, r);
                c.arcTo(x, y, x + w, y, r);
                c.closePath();
                return c;
            }
        };

        ig.consoleHelper = {
            styles: {
                color: 'green',
                fontSize: '12px',
                fontWeight: 'normal',
                background: 'none',
                textDecoration: 'none'
            },
            setStyle: function (newStyles) {
                for (var property in newStyles) {
                    if (newStyles.hasOwnProperty(property)) {
                        this.styles[property] = newStyles[property];
                    }
                }
            },
            resetStyle: function () {
                this.styles = {
                    color: 'green',
                    fontSize: '12px',
                    fontWeight: 'normal',
                    background: 'none',
                    textDecoration: 'none'
                };
            },
            log: function (msg) {
                var styleString = '';
                for (var property in this.styles) {
                    if (this.styles.hasOwnProperty(property)) {
                        styleString += property + ':' + this.styles[property] + ';';
                    }
                }
                console.log('%c' + msg, styleString);
            },
            clear: function () {
                console.clear();
            }
        };
    });
