ig.module('game.entities.others.tween-template')
.requires('impact.entity')
.defines(function () {
    EntityTweenTemplate = ig.Entity.extend({
        zIndex: 10,
        fadeAlpha: 1,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            ig.game.sortEntitiesDeferred();
        },

        fadeToWhite: function (callback) {
            var tweenAlpha = { fadeAlpha: 0 };
            var tween = new ig.TweenDef(tweenAlpha)
            .to({ fadeAlpha: 1 }, 200)
            .onUpdate(function () {
                this.fadeAlpha = tweenAlpha.fadeAlpha;
            }.bind(this))
            .onComplete(function () {
                if (callback) {
                    callback();
                }
            }.bind(this))
            .start();
        },

        fadeIn: function (callback) {
            var tweenAlpha = { fadeAlpha: 1 };
            var tween = new ig.TweenDef(tweenAlpha)
            .to({ fadeAlpha: 0 }, 500)
            .onUpdate(function () {
                this.fadeAlpha = tweenAlpha.fadeAlpha;
            }.bind(this))
            .onComplete(function () {
                if (callback) {
                    callback();
                }
            }.bind(this))
            .start();
        },

        drawGlobalAlpha: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.fillStyle = 'rgba(255, 255, 255, ' + this.fadeAlpha + ')';
            ctx.fillRect(0, 0, ig.system.width, ig.system.height);
            ctx.restore();
        },

        draw: function () {
            this.drawGlobalAlpha();
        },

        update: function () {
            this.parent();
        }
    });
});
