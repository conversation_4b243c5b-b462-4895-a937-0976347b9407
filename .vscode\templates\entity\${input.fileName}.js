ig.module('game.${_toDotCase(relativeFileDirname.split("\\").pop())}.${fileBasenameNoExtension}.${input.fileName}')
.requires(
    'impact.entity'
)
.defines(function () {
    "use strict";

    ig.Entity${_toPascalCase(input.fileName)} = ig.global.Entity${_toPascalCase(input.fileName)} = ig.Entity.extend({
        name: '${input.fileName}',
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            ig.game.sortEntitiesDeferred();
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
        }
    });
});
