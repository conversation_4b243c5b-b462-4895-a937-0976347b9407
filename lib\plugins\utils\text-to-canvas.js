ig.module('plugins.utils.text-to-canvas')
.requires()
.defines(function () {
    var _excluded = ["ctx", "words", "justify", "format", "inferWhitespace"], _excluded2 = ["text"]; function _createForOfIteratorHelper (t, e){var r = "undefined" != typeof Symbol && t[Symbol.iterator] || t["@@iterator"]; if (!r){if (Array.isArray(t) || (r = _unsupportedIterableToArray(t)) || e && t && "number" == typeof t.length){r && (t = r); var n = 0, o = function (){}; return { s: o, n: function (){return n >= t.length ? { done: !0 } : { done: !1, value: t[n++] };}, e: function (t){throw t;}, f: o };} throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");} var i, a = !0, f = !1; return { s: function (){r = r.call(t);}, n: function (){var t = r.next(); return a = t.done, t;}, e: function (t){f = !0, i = t;}, f: function (){try {a || null == r.return || r.return();} finally {if (f) throw i;}} };} function _objectWithoutProperties (t, e){if (null == t) return {}; var r, n, o = _objectWithoutPropertiesLoose(t, e); if (Object.getOwnPropertySymbols){var i = Object.getOwnPropertySymbols(t); for (n = 0; n < i.length; n++)r = i[n], e.includes(r) || {}.propertyIsEnumerable.call(t, r) && (o[r] = t[r]);} return o;} function _objectWithoutPropertiesLoose (t, e){if (null == t) return {}; var r = {}; for (var n in t) if ({}.hasOwnProperty.call(t, n)){if (e.includes(n)) continue; r[n] = t[n];} return r;} function _toConsumableArray (t){return _arrayWithoutHoles(t) || _iterableToArray(t) || _unsupportedIterableToArray(t) || _nonIterableSpread();} function _nonIterableSpread (){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");} function _unsupportedIterableToArray (t, e){if (t){if ("string" == typeof t) return _arrayLikeToArray(t, e); var r = {}.toString.call(t).slice(8, -1); return "Object" === r && t.constructor && (r = t.constructor.name), "Map" === r || "Set" === r ? Array.from(t) : "Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r) ? _arrayLikeToArray(t, e) : void 0;}} function _iterableToArray (t){if ("undefined" != typeof Symbol && null != t[Symbol.iterator] || null != t["@@iterator"]) return Array.from(t);} function _arrayWithoutHoles (t){if (Array.isArray(t)) return _arrayLikeToArray(t);} function _arrayLikeToArray (t, e){(null == e || e > t.length) && (e = t.length); for (var r = 0, n = Array(e); r < e; r++)n[r] = t[r]; return n;} function ownKeys (t, e){var r = Object.keys(t); if (Object.getOwnPropertySymbols){var n = Object.getOwnPropertySymbols(t); e && (n = n.filter((function (e){return Object.getOwnPropertyDescriptor(t, e).enumerable;}))), r.push.apply(r, n);} return r;} function _objectSpread (t){for (var e = 1; e < arguments.length; e++){var r = null != arguments[e] ? arguments[e] : {}; e % 2 ? ownKeys(Object(r), !0).forEach((function (e){_defineProperty(t, e, r[e]);})) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : ownKeys(Object(r)).forEach((function (e){Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e));}));} return t;} function _defineProperty (t, e, r){return (e = _toPropertyKey(e)) in t ? Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }) : t[e] = r, t;} function _toPropertyKey (t){var e = _toPrimitive(t, "string"); return "symbol" == _typeof(e) ? e : e + "";} function _toPrimitive (t, e){if ("object" != _typeof(t) || !t) return t; var r = t[Symbol.toPrimitive]; if (void 0 !== r){var n = r.call(t, e || "default"); if ("object" != _typeof(n)) return n; throw new TypeError("@@toPrimitive must return a primitive value.");} return ("string" === e ? String : Number)(t);} function _typeof (t){return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (t){return typeof t;} : function (t){return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t;}, _typeof(t);}!function (t, e){"object" == ("undefined" == typeof exports ? "undefined" : _typeof(exports)) && "undefined" != typeof module ? e(exports) : "function" == typeof define && define.amd ? define(["exports"], e) : e((t = "undefined" != typeof globalThis ? globalThis : t || self).textToCanvas = {});}(this, (function (t){"use strict"; var e, r = "Arial", n = "black", o = n, i = function (t, e){return Object.assign({}, { fontFamily: r, fontSize: 14, fontWeight: "400", fontStyle: "", fontVariant: "", fontColor: n, strokeColor: o, strokeWidth: 0 }, e, t);}, a = function (t){var e = t.fontFamily, n = t.fontSize, o = t.fontStyle, i = t.fontVariant, a = t.fontWeight; return "".concat(o || "", " ").concat(i || "", " ").concat(a || "", " ").concat(null != n ? n : 14, "px ").concat(e || r).trim();}, f = function (t){return !!t.match(/^\s+$/);}, u = function (t, e){if (t.length <= 1 || e.length < 1) return _toConsumableArray(t); var r = []; return t.forEach((function (n, o){r.push(n), o < t.length - 1 && e.forEach((function (t){return r.push(function (t){var e = _objectSpread({}, t); return t.format && (e.format = _objectSpread({}, t.format)), e;}(t));}));})), r;}, l = function (t){var e = t.line, r = t.spaceWidth, n = t.spaceChar, o = t.boxWidth, i = function (t){return t.filter((function (t){return !f(t.text);}));}(e); if (i.length <= 1) return e.concat(); var a = i.reduce((function (t, e){var r, n; return t + (null != (n = null == (r = e.metrics) ? void 0 : r.width) ? n : 0);}), 0), l = (o - a) / r; if (i.length > 2){var c = Math.ceil(l / (i.length - 1)), s = Array.from({ length: c }, (function (){return { text: n };})), d = i.slice(0, i.length - 1), h = u(d, s), m = s.slice(0, Math.floor(l) - (d.length - 1) * s.length), y = i[i.length - 1]; return [].concat(_toConsumableArray(h), _toConsumableArray(m), [y]);} var p = Array.from({ length: Math.floor(l) }, (function (){return { text: n };})); return u(i, p);}, c = function (t){var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "both", r = 0; if ("left" === e || "both" === e){for (;r < t.length && f(t[r].text); r++);if (r >= t.length) return { trimmedLeft: t.concat(), trimmedRight: [], trimmedLine: [] };} var n = t.length; if ("right" === e || "both" === e){for (n--; n >= 0 && f(t[n].text); n--);if (++n <= 0) return { trimmedLeft: [], trimmedRight: t.concat(), trimmedLine: [] };} return { trimmedLeft: t.slice(0, r), trimmedRight: t.slice(n), trimmedLine: t.slice(r, n) };}, s = function (t){return "".concat(t.text).concat(t.format ? JSON.stringify(t.format) : "");}, d = function (t, e){if ("metrics" === t && e && "object" == _typeof(e)){var r = e; return { width: r.width, fontBoundingBoxAscent: r.fontBoundingBoxAscent, fontBoundingBoxDescent: r.fontBoundingBoxDescent };} return e;}, h = function (t){var r = t.ctx, n = t.word, o = t.wordMap, f = t.baseTextFormat, u = s(n); if (n.metrics){var l; if (!o.has(u))n.format && (l = i(n.format, f)), o.set(u, { metrics: n.metrics, format: l }); return n.metrics.width;} if (o.has(u)){var c = o.get(u).metrics; return n.metrics = c, c.width;} var d, h = !1; n.format && (r.save(), h = !0, d = i(n.format, f), r.font = a(d)), e || (h || (r.save(), h = !0), r.textBaseline = "bottom"); var m = r.measureText(n.text); return "number" == typeof m.fontBoundingBoxAscent ? e = !0 : (e = !1, m.fontBoundingBoxAscent = m.actualBoundingBoxAscent, m.fontBoundingBoxDescent = 0), n.metrics = m, o.set(u, { metrics: m, format: d }), h && r.restore(), m.width;}, m = function (t){var e = t.ctx, r = t.words, n = t.justify, o = t.format, u = t.inferWhitespace, d = void 0 === u || u, m = _objectWithoutProperties(t, _excluded), y = new Map, p = i(o), g = m.width, v = function (t){var r = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], n = 0, o = 0; return t.every((function (t, i){var a = h({ ctx: e, word: t, wordMap: y, baseTextFormat: p }); return !r && n + a > g ? (0 === i && (o = 1, n = a), !1) : (o++, n += a, !0);})), { lineWidth: n, splitPoint: o };}; e.save(); var b = function (t){var e = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1], r = [[]], n = !1; return t.forEach((function (t, o){var i, a, u; if (t.text.match(/^\n+$/)){for (var l = 0; l < t.text.length; l++)r.push([]); n = !0;} else {if (f(t.text)) return null == (i = r.at(-1)) || i.push(t), void(n = !0); "" !== t.text && (e && !n && o > 0 && (null == (a = r.at(-1)) || a.push({ text: " " })), null == (u = r.at(-1)) || u.push(t), n = !1);}})), r;}(c(r).trimmedLine, d); if (b.length <= 0 || g <= 0 || m.height <= 0 || o && "number" == typeof o.fontSize && o.fontSize <= 0) return { lines: [], textAlign: "center", textBaseline: "middle", width: m.width, height: 0 }; e.font = a(p); var x, w = n ? h({ ctx: e, word: { text: " " }, wordMap: y, baseTextFormat: p }) : 0, S = [], A = _createForOfIteratorHelper(b); try {for (A.s(); !(x = A.n()).done;){var _ = x.value, j = v(_).splitPoint; if (j >= _.length)S.push(_); else {for (var W = _.concat(); j < W.length;){var T = c(W.slice(0, j), "right").trimmedLine; S.push(T), W = c(W.slice(j), "left").trimmedLine, j = v(W).splitPoint;}S.push(W);}}} catch (t){A.e(t);} finally {A.f();}n && S.length > 1 && S.forEach((function (t, e){if (e < S.length - 1){var r = l({ line: t, spaceWidth: w, spaceChar: " ", boxWidth: g }); v(r, !0), S[e] = r;}})); var P = function (t){var e, r, n = t.wrappedLines, o = t.wordMap, i = t.positioning, a = i.width, u = i.height, l = i.x, c = void 0 === l ? 0 : l, d = i.y, h = void 0 === d ? 0 : d, m = i.align, y = i.vAlign, p = c + a, g = h + u, v = function (t){return t.metrics.fontBoundingBoxAscent + t.metrics.fontBoundingBoxDescent;}, b = n.map((function (t){return t.reduce((function (t, e){return Math.max(t, v(e));}), 0);})), x = b.reduce((function (t, e){return t + e;}), 0); return "top" === y ? (r = "top", e = h) : "bottom" === y ? (r = "bottom", e = g - x) : (r = "top", e = h + u / 2 - x / 2), { lines: n.map((function (t, r){var n = t.reduce((function (t, e){return t + e.metrics.width;}), 0), i = b[r], u = "right" === m ? p - n : "left" === m ? c : c + a / 2 - n / 2, l = t.map((function (t){var r, n = s(t), a = o.get(n).format, l = u, c = v(t); return r = "top" === y ? e : "bottom" === y ? e + i : e + (i - c) / 2, u += t.metrics.width, { word: t, format: a, x: l, y: r, width: t.metrics.width, height: c, isWhitespace: f(t.text) };})); return e += i, l;})), textBaseline: r, textAlign: "left", width: a, height: x };}({ wrappedLines: S, wordMap: y, positioning: m }); return e.restore(), P;}, y = function (t){var e, r = [], n = !1; return Array.from(t.trim()).forEach((function (t){var o = f(t); o && !n || !o && n ? (n = o, e && r.push(e), e = { text: t }) : (e || (e = { text: "" }), e.text += t);})), e && r.push(e), r;}, p = function (t, e, r){var n = t.textBaseline, o = t.font; t.textBaseline = "bottom", r && (t.font = r); var i = t.measureText(e).actualBoundingBoxAscent; return t.textBaseline = n, r && (t.font = o), i;}; t.drawText = function (t, e, r){var f, u = i({ fontFamily: r.fontFamily, fontSize: r.fontSize, fontStyle: r.fontStyle, fontVariant: r.fontVariant, fontWeight: r.fontWeight, fontColor: r.fontColor, strokeColor: r.strokeColor, strokeWidth: r.strokeWidth }), l = r.width, c = r.height, s = r.x, d = void 0 === s ? 0 : s, h = r.y, p = void 0 === h ? 0 : h, g = m({ ctx: t, words: Array.isArray(e) ? e : y(e), inferWhitespace: Array.isArray(e) ? void 0 === r.inferWhitespace || r.inferWhitespace : void 0, x: d, y: p, width: r.width, height: r.height, align: r.align, vAlign: r.vAlign, justify: r.justify, format: u }), v = g.lines, b = g.height, x = g.textBaseline, w = g.textAlign; t.save(), t.textAlign = w, t.textBaseline = x, t.font = a(u), t.fillStyle = u.fontColor || n, t.strokeStyle = u.strokeColor || o, t.lineJoin = "round"; var S = null != (f = u.strokeWidth) ? f : 0; if (!1 === r.overflow && (t.beginPath(), t.rect(d, p, l, c), t.clip()), v.forEach((function (e){e.forEach((function (e){var r; if (!e.isWhitespace){e.format && (t.save(), t.font = a(e.format), e.format.fontColor && (t.fillStyle = e.format.fontColor), e.format.strokeColor && (t.strokeStyle = e.format.strokeColor)), t.fillText(e.word.text, e.x, e.y); var n = "number" == typeof(null == (r = e.format) ? void 0 : r.strokeWidth) ? e.format.strokeWidth : S; n > 0 && (t.lineWidth = n, t.strokeText(e.word.text, e.x, e.y)), e.format && t.restore();}}));})), r.debug){var A, _ = d + l, j = p + c; A = "right" === r.align ? _ : "left" === r.align ? d : d + l / 2; var W = p; "bottom" === r.vAlign ? W = j : "middle" === r.vAlign && (W = p + c / 2); var T = "#f44336"; t.lineWidth = 3, t.strokeStyle = T, t.strokeRect(d, p, l, c), t.lineWidth = 1, (!r.align || "center" === r.align) && (t.strokeStyle = T, t.beginPath(), t.moveTo(A, p), t.lineTo(A, j), t.stroke()), (!r.vAlign || "middle" === r.vAlign) && (t.strokeStyle = T, t.beginPath(), t.moveTo(d, W), t.lineTo(_, W), t.stroke());} return t.restore(), { height: b };}, t.getTextFormat = i, t.getTextHeight = function (t){var e = t.ctx, r = t.text, n = t.style; return p(e, r, n);}, t.getTextStyle = a, t.getWordHeight = function (t){var e = t.ctx, r = t.word; return p(e, r.text, r.format && a(r.format));}, t.specToJson = function (t){return JSON.stringify(t, d);}, t.splitText = function (t){var e = t.text, r = _objectWithoutProperties(t, _excluded2), n = y(e); return m(_objectSpread(_objectSpread({}, r), {}, { words: n, inferWhitespace: !1 })).lines.map((function (t){return t.map((function (t){return t.word.text;})).join("");}));}, t.splitWords = m, t.textToWords = y, t.wordsToJson = function (t){return JSON.stringify(t, d);}, Object.defineProperty(t, Symbol.toStringTag, { value: "Module" });}));
    ig.textToCanvas = window.textToCanvas;

    // Note: ig.TextToCanvasMixin has been removed.
    // Public text APIs have been consolidated in 'plugins.utils.text' as:
    //  - ig.Text (service: defaults, draw, measure)
    //  - ig.TextLabelMixin (mixin to add text to entities)
    //  - EntityText (dedicated text entity)
    // This module now only exposes the low-level engine via ig.textToCanvas.
});
