ig.module('game.entities.buttons.button-more-games')
	.requires(
		'plugins.utils.buttons.button-image',
		'plugins.clickable-div-layer'
	)
	.defines(function () {
		EntityButtonMoreGames = EntityButtonImage.extend({
			zIndex: 11,
			clickableLayer: null,
			link: null,
			newWindow: true,
			div_layer_name: 'more-games',
			name: 'moregames',
			idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/button/button-more-games.png'), frameCountX: 1, frameCountY: 1 },
			init: function (x, y, settings) {
				this.parent(x, y, settings);

				if (ig.global.wm) {
					return;
				}

				if (!_SETTINGS.MoreGames.Enabled) {
					this.kill();
					return;
				}

				if (settings.div_layer_name) {
					this.div_layer_name = settings.div_layer_name;
				} else {
					this.div_layer_name = 'more-games';
				}

				if (_SETTINGS.MoreGames.Link) {
					this.link = _SETTINGS.MoreGames.Link;
				}

				if (_SETTINGS.MoreGames.NewWindow) {
					this.newWindow = _SETTINGS.MoreGames.NewWindow;
				}

				this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
				this.currentAnim = this.idle;
				this.clickableLayer = new ClickableDivLayer(this);
				this.repos();
				
				// Add sink effect
				var elem = ig.domHandler.getElementById('#' + this.div_layer_name);
				ig.domHandler.addEvent(elem, 'mousedown', function (e) {
					this.clicked();
				}.bind(this));
				ig.domHandler.addEvent(elem, 'mouseup', function (e) {
					this.released();
				}.bind(this));
			},
			show: function () {
				this.disabled = false;
				var elem = ig.domHandler.getElementById('#' + this.div_layer_name);
				if (elem) {
					ig.domHandler.show(elem);
				}
			},
			hide: function () {
				this.disabled = true;
				var elem = ig.domHandler.getElementById('#' + this.div_layer_name);
				if (elem) {
					ig.domHandler.hide(elem);
				}
			},
			repos: function () {
				// console.log('Repos Moregames');
				this.clickableLayer && this.clickableLayer.updatePos(this.pos.x - ig.game.screen.x, this.pos.y - ig.game.screen.y);
			}
		});
	});
