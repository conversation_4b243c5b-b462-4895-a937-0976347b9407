/**
 * Grid System for ImpactJS - Cell-based positioning system
 * Compatible with ES5
 * Drag handling removed as it's now managed by the pointer plugin
 */
ig.module(
    'game.entities.grid-system'
)
.requires(
    'impact.entity'
)
.defines(function () {
    EntityGridSystem = ig.Entity.extend({
        // Configuration
        size: { x: 500, y: 400 },
        cellSize: 80,
        gridColor: '#333',
        highlightColor: '#5f5',
        
        // Internal properties
        cells: [],
        hoveredCell: null,
        zIndex: 10,
        
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            
            // Calculate grid dimensions
            this.cols = Math.floor(this.size.x / this.cellSize);
            this.rows = Math.floor(this.size.y / this.cellSize);
            
            // Initialize grid data structure
            this.initializeGrid();
        },
        
        initializeGrid: function () {
            this.cells = [];
            
            // Create a 2D array representing our grid
            for (var row = 0; row < this.rows; row++) {
                this.cells[row] = [];
                for (var col = 0; col < this.cols; col++) {
                    this.cells[row][col] = {
                        entity: null,
                        x: this.pos.x + (col * this.cellSize),
                        y: this.pos.y + (row * this.cellSize)
                    };
                }
            }
        },
        
        update: function () {
            this.parent();
            this.updateHoveredCell();
        },
        
        draw: function () {
            this.parent();
            this.drawGrid();
            
            // Draw hover highlight if needed
            if (this.hoveredCell) {
                var cell = this.cells[this.hoveredCell.row][this.hoveredCell.col];
                ig.system.context.fillStyle = this.highlightColor;
                ig.system.context.globalAlpha = 0.3;
                ig.system.context.fillRect(
                    cell.x - ig.game.screen.x,
                    cell.y - ig.game.screen.y,
                    this.cellSize,
                    this.cellSize
                );
                ig.system.context.globalAlpha = 1;
            }
        },
        
        drawGrid: function () {
            var ctx = ig.system.context;
            ctx.strokeStyle = this.gridColor;
            ctx.lineWidth = 1;
            
            // Draw vertical lines
            for (var col = 0; col <= this.cols; col++) {
                var x = (this.pos.x + (col * this.cellSize)) - ig.game.screen.x;
                ctx.beginPath();
                ctx.moveTo(x, this.pos.y - ig.game.screen.y);
                ctx.lineTo(x, this.pos.y + this.size.y - ig.game.screen.y);
                ctx.stroke();
            }
            
            // Draw horizontal lines
            for (var row = 0; row <= this.rows; row++) {
                var y = (this.pos.y + (row * this.cellSize)) - ig.game.screen.y;
                ctx.beginPath();
                ctx.moveTo(this.pos.x - ig.game.screen.x, y);
                ctx.lineTo(this.pos.x + this.size.x - ig.game.screen.x, y);
                ctx.stroke();
            }
        },
        
        updateHoveredCell: function () {
            // Get mouse position
            var mouseX = ig.input.mouse.x + ig.game.screen.x;
            var mouseY = ig.input.mouse.y + ig.game.screen.y;
            
            // Check if mouse is within grid bounds
            if (mouseX >= this.pos.x && mouseX < this.pos.x + this.size.x &&
                mouseY >= this.pos.y && mouseY < this.pos.y + this.size.y) {
                
                // Calculate row and column
                var col = Math.floor((mouseX - this.pos.x) / this.cellSize);
                var row = Math.floor((mouseY - this.pos.y) / this.cellSize);
                
                this.hoveredCell = { row: row, col: col };
            } else {
                this.hoveredCell = null;
            }
        },
        
        /**
         * Attempts to place an entity in the specified cell
         * @param {ig.Entity} entity - The entity to place
         * @param {Number} row - Row index
         * @param {Number} col - Column index
         * @returns {Boolean} - True if placement succeeded
         */
        placeEntity: function (entity, row, col) {
            var cell = this.getCell(row, col);
            if (cell && !cell.entity) {
                // Position the entity in the center of the cell
                entity.pos.x = cell.x + (this.cellSize - entity.size.x) / 2;
                entity.pos.y = cell.y + (this.cellSize - entity.size.y) / 2;
                
                // Store reference to the entity
                cell.entity = entity;
                return true;
            }
            return false;
        },
        
        /**
         * Removes an entity from the cell it occupies
         * @param {ig.Entity} entity - The entity to remove
         * @returns {Object|null} - The cell that contained the entity, or null
         */
        removeEntity: function (entity) {
            for (var row = 0; row < this.rows; row++) {
                for (var col = 0; col < this.cols; col++) {
                    if (this.cells[row][col].entity === entity) {
                        var cell = this.cells[row][col];
                        this.cells[row][col].entity = null;
                        return { cell: cell, row: row, col: col };
                    }
                }
            }
            return null;
        },
        
        /**
         * Find a cell for an entity dropped at the given position
         * @param {Object} pos - World position {x, y}
         * @returns {Object|null} - Cell information or null if no suitable cell
         */
        findCellForPosition: function (pos) {
            // Check if position is within grid bounds
            if (pos.x >= this.pos.x && pos.x < this.pos.x + this.size.x &&
                pos.y >= this.pos.y && pos.y < this.pos.y + this.size.y) {
                
                // Calculate row and column
                var col = Math.floor((pos.x - this.pos.x) / this.cellSize);
                var row = Math.floor((pos.y - this.pos.y) / this.cellSize);
                
                var cell = this.getCell(row, col);
                if (cell && !cell.entity) {
                    return {
                        cell: cell,
                        row: row,
                        col: col
                    };
                }
            }
            return null;
        },
        
        /**
         * Handles an entity being dropped from the pointer plugin
         * @param {ig.Entity} entity - The dropped entity
         * @returns {Boolean} - True if the entity was placed in a cell
         */
        handleEntityDrop: function (entity) {
            // Find cell at entity's center position
            var centerPos = {
                x: entity.pos.x + entity.size.x / 2,
                y: entity.pos.y + entity.size.y / 2
            };
            
            var cellInfo = this.findCellForPosition(centerPos);
            if (cellInfo) {
                // Remove from previous cell if any
                this.removeEntity(entity);
                
                // Place in new cell
                entity.pos.x = cellInfo.cell.x + (this.cellSize - entity.size.x) / 2;
                entity.pos.y = cellInfo.cell.y + (this.cellSize - entity.size.y) / 2;
                cellInfo.cell.entity = entity;
                
                // Set entity's grid position for reference
                entity.gridPosition = {
                    row: cellInfo.row,
                    col: cellInfo.col
                };
                
                return true;
            }
            return false;
        },
        
        /**
         * Get the first empty cell in the grid
         * @returns {Object|null} - Cell information or null if grid is full
         */
        getFirstEmptyCell: function () {
            for (var row = 0; row < this.rows; row++) {
                for (var col = 0; col < this.cols; col++) {
                    if (!this.cells[row][col].entity) {
                        return {
                            cell: this.cells[row][col],
                            row: row,
                            col: col
                        };
                    }
                }
            }
            return null;
        },
        
        /**
         * Get nearest empty cell to a position
         * @param {Object} pos - World position {x, y}
         * @returns {Object|null} - Cell information or null if grid is full
         */
        getNearestEmptyCell: function (pos) {
            var nearest = null;
            var minDistance = Infinity;
            
            for (var row = 0; row < this.rows; row++) {
                for (var col = 0; col < this.cols; col++) {
                    if (!this.cells[row][col].entity) {
                        // Calculate center of cell
                        var cellCenter = {
                            x: this.cells[row][col].x + this.cellSize / 2,
                            y: this.cells[row][col].y + this.cellSize / 2
                        };
                        
                        // Calculate distance
                        var dx = cellCenter.x - pos.x;
                        var dy = cellCenter.y - pos.y;
                        var distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance < minDistance) {
                            minDistance = distance;
                            nearest = {
                                cell: this.cells[row][col],
                                row: row,
                                col: col
                            };
                        }
                    }
                }
            }
            
            return nearest;
        },
        
        // Utility method to get a cell by grid coordinates
        getCell: function (row, col) {
            if (row >= 0 && row < this.rows && col >= 0 && col < this.cols) {
                return this.cells[row][col];
            }
            return null;
        },
        
        // Utility method to get world coordinates from grid coordinates
        getCellPosition: function (row, col) {
            var cell = this.getCell(row, col);
            if (cell) {
                return { x: cell.x, y: cell.y };
            }
            return null;
        }
    });
});
