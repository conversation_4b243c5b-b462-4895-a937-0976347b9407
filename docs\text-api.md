# Text Rendering API (ig.Text, ig.TextLabelMixin, EntityText)

This document describes the consolidated text rendering API exposed by `plugins.utils.text`, built on top of the low-level layout engine in `plugins.utils.text-to-canvas`.

The goals of this API are to:
- Provide a single, ergonomic surface for drawing and controlling text
- Reduce boilerplate for common tasks (styling, positioning, animation)
- Keep backward compatibility with existing code

## Contents
- [Overview](#overview)
- [Quick start](#quick-start)
- [API reference](#api-reference)
  - [ig.Text (static service)](#igtext-static-service)
  - [ig.TextLabelMixin (instance helpers)](#igtextlabelmixin-instance-helpers)
  - [EntityText (dedicated text entity)](#entitytext-dedicated-text-entity)
  - [ig.Text.attachLabel (convenience factory)](#igtextattachlabel-convenience-factory)
- [Migration guide (from ig.TextToCanvasMixin)](#migration-guide-from-igtexttocanvasmixin)
- [Patterns and examples](#patterns-and-examples)
- [Cheat sheet](#cheat-sheet-methods-by-category)
- [Anchors and offsets](#anchors-and-offsets)
- [Troubleshooting](#troubleshooting)
- [Notes](#notes)

---

## Overview

**Layers:**
- **Low-level engine (unchanged):** `ig.textToCanvas` (from `plugins.utils.text-to-canvas`) handles layout, wrapping, justification, and canvas drawing.
- **Public API (new):** `plugins.utils.text` provides:
  - `ig.Text`: stateless helpers (draw, measure, style, positioning, animation)
  - `ig.TextLabelMixin`: a mixin you can inject into any entity for inline text
  - `EntityText`: an entity dedicated to rendering text (with offscreen caching)
  - `ig.Text.attachLabel(target, options)`: one-liner to create/anchor a label entity to a target

`plugins.utils.text-to-canvas` no longer declares `ig.TextToCanvasMixin`. The engine remains available via `ig.textToCanvas`.

---

## Quick start

### 1) Import the consolidated module wherever you need text

```js
.requires(
  'plugins.utils.text'
)
```

### 2) Use the mixin in an entity and draw

```js
EntityFoo = ig.Entity.extend({
  init: function (x, y, settings) {
    this.parent(x, y, settings);
    this.initText({
      text: 'Hello',
      width: 200, height: 60,
      fontSize: 24, align: 'center', vAlign: 'middle'
    });
    this.centerTextInEntity().fadeInText(300); // convenience
  },
  draw: function () { this.parent(); this.drawText(); }
});
// Inject once, e.g. near entity definition
if (ig.TextLabelMixin) { EntityFoo.inject(ig.TextLabelMixin); }
```

### 3) Or attach a separate label entity to an existing target

```js
this.label = ig.Text.attachLabel(this, {
  textConfig: { text: 'Play', width: this.size.x, height: this.size.y, align: 'center', vAlign: 'middle' },
  offset: { x: 0, y: 0 },
  zIndex: this.zIndex + 1,
  alpha: this.alpha,
  cache: true // uses offscreen caching by default
});
```

---

## API reference

### ig.Text (static service)

#### Minimal drawing wrappers
- `ig.Text.draw(ctx, text, config)` – draw text through the engine
- `ig.Text.getTextHeight(ctx, text, style)` – measure ascent (via engine)

#### Content helpers
- `ig.Text.getText(target)` – returns current text from an entity or a config
- `ig.Text.setText(target, text)` – sets text on `target`
- `ig.Text.appendText(target, extra)` – appends to existing text
- `ig.Text.clearText(target)` – clears text

#### Style helpers
- `ig.Text.setStyle(target, style)` – merges style into config (TODO: REVIEW overlap with `updateTextConfig`)
- `ig.Text.updateFontSize/Color/Family(target, value)`
- `ig.Text.updateAlign(target, 'left'|'center'|'right')`
- `ig.Text.updateVAlign(target, 'top'|'middle'|'bottom')`
- `ig.Text.setStroke(target, color, width)`
- `ig.Text.setShadow(target, { enabled?, offsetX?, offsetY?, blur?, color? })`

#### Positioning helpers
- `ig.Text.moveBy(entity, dx, dy)` – adjusts `entity.textOffset`
- `ig.Text.centerInEntity(entity)` – centers configured box inside `entity.size`
- `ig.Text.getDimensions(target)` – returns `{ width, height }` of the configured box

#### Utilities
- `ig.Text.measureBounds(text, config)` – returns `{ width, height }` (height is content height)
- `ig.Text.fitsInBounds(text, config)` – true if measured content height <= `config.height`
- `ig.Text.autoSizeText(target, { min?, max?, apply?, precision? })` – finds max fontSize that fits; applies if `apply: true`

#### Animation helpers
- `ig.Text.fadeIn(entity, duration=200)` – tweens `alpha` to 1
- `ig.Text.fadeOut(entity, duration=200)` – tweens `alpha` to 0
- `ig.Text.tweenText(entity, props, duration=200)` – tweens textOffset `{x,y}`, `alpha`, `visualScale` and numeric `textConfig` fields (`fontSize`, `strokeWidth`, `width`, `height`)


---

## Configuration reference (drawText config)

The underlying engine accepts the following configuration properties when drawing text. Unless noted, these map directly to the canvas layout/draw logic.

Property | Default | Description
--- | --- | ---
width | Required | Width of the text box.
height | Required | Height of the text box.
x | 0 | X position of the text box.
y | 0 | Y position of the text box.
align | center | Text align. Other possible values: left, right.
vAlign | middle | Text vertical align. Other possible values: top, bottom.
fontFamily | Arial | Base font family of the text.
fontSize | 14 | Base font size of the text in px.
fontStyle | '' | Base font style, same as CSS font-style. Examples: italic, oblique 40deg.
fontVariant | '' | Base font variant, same as CSS font-variant. Examples: small-caps.
fontWeight | '400' | Base font weight, same as CSS font-weight. Examples: bold, 100.
fontColor | 'black' | Base font color, same as CSS color. Examples: blue, #00ff00.
strokeColor | 'black' | Base stroke color, same as CSS color. Examples: blue, #00ff00.
strokeWidth | 0 | Base stroke width. Positive number; <=0 means none. Can be fractional. Warning: word splitting does not take into account the stroke, which is applied on the center of the edges of the text via the strokeText() Canvas API. Setting a thick stroke will cause it to bleed out of the text box.
justify | false | Justify text if true; it will insert spaces between words when necessary.
inferWhitespace | true | If whitespace in the text should be inferred. Only applies if the text given to drawText() is a Word[]. If the text is a string, this config setting is ignored.
overflow | true | Allows the text to overflow out of the box if the box is too narrow/short to fit it all. false will clip the text to the box's boundaries.
debug | false | Draws the border and alignment lines of the text box for debugging purposes.

Notes
- Width and height are required by the engine; the mixin may derive sensible defaults for convenience, but the renderer expects explicit sizing.
- Some higher-level features in this module (e.g., shadow properties, overflowAllowance for offscreen caching) are not part of the core engine config and are documented in the respective entity/mixin sections.

**Notes:**
- All helpers accept either an entity with `textConfig` or a plain `config` object where applicable.

---

### ig.TextLabelMixin (instance helpers)

#### Existing core methods
- `initText(config)`, `setText(text)`, `updateTextConfig(style)`, `drawText()`

#### Content
- `getTextContent()`, `setTextContent(text)`, `appendText(extra)`, `clearText()`

#### Style
- `setStyle(style)` (TODO: REVIEW overlap with `updateTextConfig`)
- `setFontSize(size)`, `setFontColor(color)`, `setFontFamily(family)`
- `setAlign(align)`, `setVAlign(vAlign)`, `setStroke(color, width)`, `setShadow({ enabled?, offsetX?, offsetY?, blur?, color? })`

#### Positioning
- `moveTextBy(dx, dy)`, `centerTextInEntity()`, `getTextDimensions()`

#### Utilities
- `measureTextBounds()`, `textFitsInBounds()`, `autoSizeCurrentText(opts)`

#### Animation
- `fadeInText(duration)`, `fadeOutText(duration)`, `scaleTextTo(scale)`, `tweenTextProps(props, duration)`

**Note:** All instance setters return `this` for chaining.

---

### EntityText (dedicated text entity)

- Uses `ig.TextLabelMixin` under the hood (injected).
- Supports offscreen canvas rendering and `ImageBitmap` caching for efficiency.
- Provides anchoring helpers (via `anchorTo`) to align text relative to another entity.
- Useful when you want labels as separate entities (e.g., buttons, popups).

---

### ig.Text.attachLabel (convenience factory)

Creates an `EntityText` anchored to `target` with sensible defaults.

#### Options
- `textConfig`: partial or full config for the label; width/height default to `target.size`
- `text`: override for `textConfig.text`
- `offset`: `{ x, y }` pixel offset, defaults to `{0,0}`
- `zIndex`: defaults to `target.zIndex + 1`
- `alpha`: defaults to `target.alpha` or `1`
- `targetAnchor`: defaults to `{ x: 0, y: 0 }` (center); use `{ x: -1, y: -1 }` for top-left
- `selfAnchor`: defaults to `'center-middle'` (text's own anchor)
- `cache`: use offscreen caching (`true` by default)

**Returns:** the spawned `EntityText` (or `null` if spawning fails).

---

## Migration guide (from ig.TextToCanvasMixin)

`ig.TextToCanvasMixin` was removed. To migrate:

#### Update requires:
- **Before:** `.requires('plugins.utils.text-to-canvas')`
- **After:** `.requires('plugins.utils.text')`

#### Update injection:
- **Before:** `EntityFoo.inject(ig.TextToCanvasMixin)`
- **After:** `EntityFoo.inject(ig.TextLabelMixin)`

#### Other notes:
- Engine usage (`ig.textToCanvas`) remains available; you can still call low-level functions if needed.
- Prefer high-level helpers: `ig.Text.draw`, `ig.Text.measureBounds`, mixin instance helpers, or `ig.Text.attachLabel`.

---

## Patterns and examples

### Center and fade a text box inside an entity
```js
this.initText({ text: 'Hello', width: 260, height: 80, align: 'center', vAlign: 'middle' });
this.centerTextInEntity().fadeInText(300);
```

### Auto-size text to fit a box
```js
this.initText({ text: 'Level Complete', width: 320, height: 96, fontSize: 48 });
this.autoSizeCurrentText({ min: 16, max: 64, apply: true });
```

### Attach a label to a button
```js
this.label = ig.Text.attachLabel(this, {
  textConfig: { text: 'Play', align: 'center', vAlign: 'middle' },
  offset: { x: 0, y: 0 }, zIndex: this.zIndex + 1, alpha: this.alpha
});
```

### Nudge a label back and forth (yoyo tween)
```js
var orig = { x: this.textOffset.x, y: this.textOffset.y };
var tw = this.tweenTextProps({ textOffset: { x: orig.x + 12, y: orig.y } }, 400);
if (tw && tw.yoyo) tw.yoyo(true).repeat(Infinity);
```

### Measure and decide whether to reduce font size
```js
var cfg = ig.copy(ig.Text.defaults);
cfg.text = 'Long Title'; cfg.width = 240; cfg.height = 60; cfg.fontSize = 48;
if (!ig.Text.fitsInBounds(cfg.text, cfg)) ig.Text.autoSizeText(cfg, { min: 16, max: 48, apply: true });
```

---

## Cheat sheet: methods by category

### Content
**Static methods:**
- `ig.Text.getText(target)`
- `ig.Text.setText(target, text)`
- `ig.Text.appendText(target, extra)`
- `ig.Text.clearText(target)`

**Instance methods:**
- `getTextContent()`, `setTextContent(text)`, `appendText(extra)`, `clearText()`

### Style
**Static methods:**
- `ig.Text.setStyle(target, style)` // TODO: REVIEW overlap with updateTextConfig
- `ig.Text.updateFontSize/Color/Family(target, value)`
- `ig.Text.updateAlign(target, 'left'|'center'|'right')`
- `ig.Text.updateVAlign(target, 'top'|'middle'|'bottom')`
- `ig.Text.setStroke(target, color, width)`
- `ig.Text.setShadow(target, { enabled?, offsetX?, offsetY?, blur?, color? })`

**Instance methods:**
- `setStyle(style)` // TODO: REVIEW
- `setFontSize(size)`, `setFontColor(color)`, `setFontFamily(family)`
- `setAlign(align)`, `setVAlign(vAlign)`, `setStroke(color, width)`, `setShadow({...})`

### Positioning
**Static methods:**
- `ig.Text.moveBy(entity, dx, dy)`
- `ig.Text.centerInEntity(entity)`
- `ig.Text.getDimensions(target)`

**Instance methods:**
- `moveTextBy(dx, dy)`, `centerTextInEntity()`, `getTextDimensions()`

### Utilities
**Static methods:**
- `ig.Text.measureBounds(text, config)`
- `ig.Text.fitsInBounds(text, config)`
- `ig.Text.autoSizeText(target, { min?, max?, apply?, precision? })`

**Instance methods:**
- `measureTextBounds()`, `textFitsInBounds()`, `autoSizeCurrentText(opts)`

### Animation
**Static methods:**
- `ig.Text.fadeIn(entity, duration)`
- `ig.Text.fadeOut(entity, duration)`
- `ig.Text.tweenText(entity, props, duration)`

**Instance methods:**
- `fadeInText(duration)`, `fadeOutText(duration)`, `scaleTextTo(scale)`, `tweenTextProps(props, duration)`

---

## Anchors and offsets

`attachLabel` uses anchors to place labels relative to a target:

### targetAnchor
`{ x, y }` where:
- `-1` = min (left/top)
- `0` = center
- `1` = max (right/bottom)

**Examples:**
- `{ x: -1, y: -1 }` = top-left
- `{ x: 0, y: 0 }` = center
- `{ x: 1, y: 1 }` = bottom-right

### selfAnchor
String describing the label's own anchor. Common values include:
- `'top-left'`, `'top-middle'`, `'top-right'`
- `'center-left'`, `'center-middle'`, `'center-right'`
- `'bottom-left'`, `'bottom-middle'`, `'bottom-right'`

### offset
`{ x, y }` pixel offset applied after anchoring.

### Defaults in attachLabel:
- `targetAnchor`: `{ x: 0, y: 0 }` (center)
- `selfAnchor`: `'center-middle'`
- `offset`: `{ x: 0, y: 0 }`

### Example: top-left aligned header on a popup
```js
this.elements.headerText = ig.Text.attachLabel(this, {
  textConfig: { text: 'Title', align: 'left', vAlign: 'top' },
  targetAnchor: { x: -1, y: -1 },
  selfAnchor: 'top-left',
  offset: { x: 12, y: 10 }
});
```

---

## Troubleshooting

### I see a SyntaxError about "Function statements require a function name"
Ensure new instance methods are added inside the `ig.TextLabelMixin = { ... }` object, not at the top-level of the module. Property entries like `getTextContent: function () { ... }` are only valid inside object literals.

### My label doesn't move with the target
Use `attachLabel` and `anchorTo` (used internally) so the label follows the target. Avoid manual `pos` updates for anchored labels.

### Text is clipped or overflowing unexpectedly
Check your `width`/`height` in the text config, and whether `overflow` is set to true. For caching, you can add a small `overflowAllowance` padding.

---

## Notes

- **Offscreen caching:** `EntityText` supports rendering onto an offscreen canvas and generating an `ImageBitmap` for efficient draw calls (enabled via `useOffscreenCanvas`).
- **Debugging:** pass `{ debug: true }` in config to visualize layout boxes and alignment lines via the engine.
- **Justification:** enable by setting `justify: true` in the text config. Whitespace inference is available via `inferWhitespace`.
