ig.module('game.entities.others.idle-utility')
.requires('impact.entity')
.defines(function () {
    String.prototype.toProperCase = function () {
        return this.replace(/\w\S*/g, function (txt) { return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(); });
    };

    EntityIdleUtility = ig.Entity.extend({
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            ig.game.idleUtility = this;
        },

        // Convert currency to string representation
        convertToString: function (money) {
            var displayMoney = money.toFixed(2); // Default value
            var foundMatch = false;

            for (var i = 0; i < this._CURRENCY.length; i++) {
                if (money >= Math.pow(10, this._CURRENCY[i].exp) && money < Math.pow(10, 93)) {
                    displayMoney = (money / Math.pow(10, this._CURRENCY[i].exp)).toFixed(2) + this._CURRENCY[i].suffix;
                    foundMatch = true;
                    break;
                }
            }

            // If no match was found in _CURRENCY, use the new naming convention
            if (!foundMatch) {
                if (money < 1000) {
                    displayMoney = money.toString();
                } else {
                    var exp = this.exponentLookup(money);
                    var magnitude = money / Math.pow(10, exp - (exp - 93) % 3);
                    displayMoney = magnitude.toFixed(2) + ' ' + this._getSuffixForExpGreaterThan90(exp).toProperCase();
                }
            }

            return displayMoney;
        },

        _getSuffixForExpGreaterThan90: function (exp) {
            var consonants = 'BCDFGHJKLMNPQRSTVWXYZ';
            var vowels = 'AEIOU';

            // Adjust the exp to account for the pattern
            var adjustedExp = Math.floor((exp - 93) / 3);

            // Convert the adjusted exp to base-26 system
            var firstChar = String.fromCharCode(66 + Math.floor(adjustedExp / 26)); // A-Z
            var secondChar = String.fromCharCode(65 + (adjustedExp % 26)); // A-Z
            // console.log(adjustedExp, firstChar, secondChar)
            var suffix = '';
            if (consonants.includes(firstChar) && vowels.includes(secondChar)) {
                suffix = firstChar + secondChar;
            } else if (consonants.includes(firstChar) && consonants.includes(secondChar)) {
                suffix = firstChar + 'A' + secondChar + 'A';
            } else if (vowels.includes(firstChar) && vowels.includes(secondChar)) {
                suffix = 'B' + firstChar + 'B' + secondChar;
            } else if (vowels.includes(firstChar) && consonants.includes(secondChar)) {
                suffix = firstChar + secondChar + 'A';
            }
            // console.log(suffix)

            return suffix + 'zillion'.toProperCase();
        },

        // Get the exponent of the currency
        exponentLookup: function (currency) {
            var remainder = Math.log(currency) / Math.log(10) % 1;
            if (remainder > 0.9)
                { return Math.ceil(Math.log(currency) / Math.log(10)); }
            else
                { return Math.floor(Math.log(currency) / Math.log(10)); }
        },

        // Convert price and exponent to actual price
        getPriceFromExp: function (price, exp) {
            if (exp <= 0) return price;
            return price * Math.pow(10, exp);
        },

        // Calculate the upgrade price
        getPriceUpgrade: function (data, level, multiplier, coef) {
            if (multiplier > 1) {
                var price = this.getPriceFromExp(data.BaseCost, data.CostExp) * (Math.pow(coef, level) * (Math.pow(coef, multiplier) - 1)) / (coef - 1);
                return price;
            } else {
                return this.getPriceFromExp(data.BaseCost, data.CostExp) * Math.pow(coef, level);
            }
        },

        // Calculate the maximum upgrade possible
        getMaxUpgrade: function (data, level, coef) {
            var CONSTANT_FACTOR = 400000000000 / 9;
            var blog = (ig.game.sessionData.playerMoney * (coef - 1)) / (this.getPriceFromExp(data.InitialCost, 0) * Math.pow(coef, level)) + 1;
            var buyMultiplier = Math.floor(Math.log10(blog) / Math.log10(data.Coefficient));

            return buyMultiplier <= 0 ? 1 : buyMultiplier;
        },

        // Calculate total angels earned
        calculateTotalAngel: function () {
            var CONSTANT_FACTOR = 400000000000 / 9;
            if (!ig.game.sessionData.lifetimeEarning) {
                ig.game.sessionData.lifetimeEarning = Number.MAX_VALUE;
            }

            if (!ig.game.sessionData.startingLifetimeEarning) {
                ig.game.sessionData.startingLifetimeEarning = Number.MAX_VALUE;
            }

            var totalInvestor = Math.pow(ig.game.sessionData.lifetimeEarning / CONSTANT_FACTOR, 0.5) - Math.pow(ig.game.sessionData.startingLifetimeEarning / CONSTANT_FACTOR, 0.5);
            this.investorGain = Math.floor(totalInvestor);
            return this.investorGain;
        },

        // TODO: Implement save and load functions
        // TODO: Implement achievement check function
        // TODO: Implement time-based progression function

        _CURRENCY: [
            { exp: 90, suffix: ' Zuillion' },
            { exp: 87, suffix: ' Jullion' },
            { exp: 84, suffix: ' Vullion' },
            { exp: 81, suffix: ' Rullion' },
            { exp: 78, suffix: ' Hullion' },
            { exp: 75, suffix: ' Fullion' },
            { exp: 72, suffix: ' Tullion' },
            { exp: 69, suffix: ' Kullion' },
            { exp: 66, suffix: ' Zillion' },
            { exp: 63, suffix: ' Jillion' },
            { exp: 60, suffix: ' Villion' },
            { exp: 57, suffix: ' Rillion' },
            { exp: 54, suffix: ' Hillion' },
            { exp: 51, suffix: ' Fillion' },
            { exp: 48, suffix: ' Tillion' },
            { exp: 45, suffix: ' Killion' },
            { exp: 42, suffix: ' Illion' },
            { exp: 39, suffix: ' Zallion' },
            { exp: 36, suffix: ' Jallion' },
            { exp: 33, suffix: ' Vallion' },
            { exp: 30, suffix: ' Rallion' },
            { exp: 27, suffix: ' Hallion' },
            { exp: 24, suffix: ' Fallion' },
            { exp: 21, suffix: ' Tallion' },
            { exp: 18, suffix: ' Kallion' },
            { exp: 15, suffix: ' Allion' },
            { exp: 12, suffix: ' Trillion' },
            { exp: 9, suffix: ' Billion' },
            { exp: 6, suffix: ' Million' },
            { exp: 3, suffix: ' Thousand' }
        ]
    });
});
