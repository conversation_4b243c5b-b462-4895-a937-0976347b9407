ig.module(
    'plugins.spotlight'
)
.requires(
    'impact.entity'
)
.defines(function (){

    Spotlight = ig.Entity.extend({
        // The target entity to highlight
        targetEntity: null,

        // Spotlight properties
        finalRadius: 50,          // The final (minimum) radius provided by the user
        maxRadius: 300,           // Automatically calculated
        currentRadius: 300,       // Current radius, starts at max
        closingSpeed: 2,          // Speed at which the spotlight closes
        easing: 0.05,             // Easing factor for smooth transition

        // Overlay color and opacity
        overlayColor: 'rgba(0, 0, 0, 0.7)',

        // Control the animation state
        isClosing: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            // Calculate maxRadius based on finalRadius
            this.finalRadius = settings.finalRadius || this.finalRadius;
            this.maxRadius = this.calculateMaxRadius(this.finalRadius);
            this.currentRadius = this.maxRadius;
            // Ensure the overlay covers the entire screen
            this.size.x = ig.system.width;
            this.size.y = ig.system.height;
            ig.game.sortEntitiesDeferred();
        },

        /**
         * Calculate the optimal maxRadius based on the finalRadius and screen size
         * You can customize this logic to fit your game's needs
         */
        calculateMaxRadius: function (finalRadius) {
            // Set maxRadius to cover the entire screen diagonally from the target
            var screenDiagonal = Math.sqrt(ig.system.width * ig.system.width + ig.system.height * ig.system.height);
            // Ensure maxRadius is at least a multiple of finalRadius
            return Math.max(screenDiagonal / 2, finalRadius * 6);
        },

        update: function () {
            this.parent();
            if (this.isClosing && this.currentRadius > this.finalRadius){
                // Smoothly interpolate the currentRadius towards finalRadius using easing
                this.currentRadius -= (this.currentRadius - this.finalRadius) * this.easing;

                // Clamp the currentRadius to finalRadius when close enough
                if (this.currentRadius <= this.finalRadius + 1){
                    this.currentRadius = this.finalRadius;
                    this.isClosing = false; // Stop closing when final radius is reached
                }
            }
        },

        draw: function () {
            var ctx = ig.system.context;

            // Save the current state of the canvas
            ctx.save();

            if (this.targetEntity) {
                // Calculate the center position of the target entity relative to the screen
                var targetX = this.targetEntity.pos.x + this.targetEntity.size.x / 2 - ig.game.screen.x;
                var targetY = this.targetEntity.pos.y + this.targetEntity.size.y / 2 - ig.game.screen.y;

                // Calculate inner radius ensuring it's not negative
                var innerRadius = Math.max(this.currentRadius * 0.5, 0); // 30% of currentRadius

                // Create a radial gradient based on the currentRadius
                var gradient = ctx.createRadialGradient(
                    targetX, targetY, innerRadius, // Inner circle (transparent)
                    targetX, targetY, this.currentRadius      // Outer circle (semi-transparent dark)
                );

                // Define gradient color stops for smooth transition
                gradient.addColorStop(0, 'rgba(0, 0, 0, 0)');          // Transparent center
                gradient.addColorStop(0.8, this.overlayColor);          // Darkened edges
                gradient.addColorStop(1, this.overlayColor);            // Fully dark at outer edge

                // Set the fill style to the gradient
                ctx.fillStyle = gradient;

                // Draw the gradient over the entire screen
                ctx.fillRect(0, 0, this.size.x, this.size.y);
            } else {
                // If no target entity is set, fill the screen with the overlay color
                ctx.fillStyle = this.overlayColor;
                ctx.fillRect(0, 0, this.size.x, this.size.y);
            }

            // Restore the previous state of the canvas
            ctx.restore();

            // Call the parent draw method (in case other drawing is needed)
            this.parent();
        },

        reset: function () {
            this.maxRadius = this.calculateMaxRadius(this.finalRadius);
            this.currentRadius = this.maxRadius;
            this.isClosing = true;
        }
    });

    ig.Entity.inject({
        spotlightOverlay: null,

        /**
         * Method to activate the spotlight
         * @param {Object} params - Configuration parameters
         * @param {number} params.finalRadius - The final radius of the spotlight
         * @param {number} [params.closingSpeed] - Optional: Speed at which the spotlight closes
         * @param {number} [params.easing] - Optional: Easing factor for the closing animation
         * @param {string} [params.overlayColor] - Optional: Color and opacity of the overlay
         */
        activateSpotlight: function (params) {
            if (!this.spotlightOverlay){
                this.__zIndex = this.zIndex;
                /* Ensure the spotlight is on top of other entities but below POPUP and NOTIFICATION
                *  Adjust based on project settings
                */
                this.zIndex = 250;
                params = params || {};
                var finalRadius = params.finalRadius;
                if (!finalRadius) {
                    console.error('activateSpotlight requires a finalRadius parameter.');
                    return;
                }
                // Spawn the Spotlight entity with only finalRadius
                this.spotlightOverlay = ig.game.spawnEntity(Spotlight, 0, 0, {
                    targetEntity: this,
                    finalRadius: finalRadius,
                    closingSpeed: params.closingSpeed || 2,
                    easing: params.easing || 0.05, // Added easing parameter
                    overlayColor: params.overlayColor || 'rgba(0, 0, 0, 0.7)',
                    zIndex: this.zIndex - 1
                });
            } else {
                this.spotlightOverlay.reset();
            }
        },

        /**
         * Method to deactivate the spotlight
         */
        deactivateSpotlight: function () {
            if (this.spotlightOverlay){
                this.zIndex = this.__zIndex;
                this.spotlightOverlay.kill();
                this.spotlightOverlay = null;
            }
        }
    });

});
