ig.module("plugins.textwrapper")
.requires()
.defines(function() {
    ig.Textwrapper = ig.Class.extend({
        textSize: 14,
        textFont: "Impact, Charcoal, sans-serif",
        textLineHeight: 1.2,
        padding: 10,
        textColor: '#000000',
        textAlign: 'center',
        textBaseline: 'middle',
        textStroke: null,
        textStrokeWidth: 2,
        strokeEnabled: false,
        containerHeight: 0,
        init: function(options) {
            if (options) {
                this.textSize = options.size || this.textSize;
                this.textFont = options.font || this.textFont;
                this.padding = options.padding || this.padding;
                this.textColor = options.color || this.textColor;
                this.textAlign = options.align || this.textAlign;
                this.textBaseline = options.baseline || this.textBaseline;
                this.textStroke = options.stroke || this.textStroke;
                this.textStrokeWidth = options.strokeWidth || this.textStrokeWidth;
                this.strokeEnabled = options.strokeEnabled !== undefined ? options.strokeEnabled : this.strokeEnabled;
                this.containerHeight = options.containerHeight || this.containerHeight;
            }
        },

        wrapAndFitText: function(text, maxWidth, maxHeight) {
            if (!text || text === "" || typeof maxWidth !== "number" || typeof maxHeight !== "number" || maxWidth <= 0 || maxHeight <= 0) {
                return [];
            }
        
            var ctx = ig.system.context;
            ctx.font = this.textSize + "px " + this.textFont;
        
            var lines = text.split("<br>");
            var wrappedLines = [];
        
            for (var l = 0; l < lines.length; l++) {
                var words = lines[l].split(" ");
                var line = "";
        
                for (var n = 0; n < words.length; n++) {
                    var testLine = line + words[n] + " ";
                    var metrics = ctx.measureText(testLine);
                    var testWidth = metrics.width;
                    if (testWidth > maxWidth && n > 0) {
                        wrappedLines.push(line);
                        line = words[n] + " ";
                    } else {
                        line = testLine;
                    }
                }
                wrappedLines.push(line);
            }
        
            // Check if the wrapped text fits within maxHeight
            var totalHeight = wrappedLines.length * this.textSize * this.textLineHeight;
            if (maxHeight > 0 && totalHeight > maxHeight) {
                var newFontSize = this.textSize * (maxHeight / totalHeight);
                this.textSize = newFontSize;
                // console.log("Text does not fit within maxHeight. New font size: " + newFontSize);
                return this.wrapAndFitText(text, maxWidth, maxHeight);
            }
        
            return wrappedLines;
        },

        drawText: function(textList, x, y, containerHeight) {
            if (!textList || textList.length <= 0) return;
            if (typeof containerHeight !== "number" || containerHeight <= 0) containerHeight = this.containerHeight;
            var ctx = ig.system.context;
            ctx.save();
            ctx.font = this.textSize + "px " + this.textFont;
            ctx.textAlign = this.textAlign;
            ctx.textBaseline = this.textBaseline;

            var lineHeight = this.textSize * this.textLineHeight;
            var totalHeight = textList.length * lineHeight - (lineHeight - this.textSize);

            var startY = y + (containerHeight - totalHeight) / 2;
            var startX = x + this.padding;

            for (var i = 0; i < textList.length; i++) {
                if (this.strokeEnabled) {
                    ctx.strokeStyle = this.textStroke;
                    ctx.lineWidth = this.textStrokeWidth;
                    ctx.lineJoin = 'round';
                    ctx.miterLimit = 2;
                    ctx.strokeText(textList[i], startX, startY);
                    ctx.globalCompositeOperation = 'destination-out';
                    ctx.fillText(textList[i], startX, startY);
                    ctx.globalCompositeOperation = 'source-over';
                }

                ctx.fillStyle = this.textColor;
                ctx.fillText(textList[i], startX, startY);

                startY += lineHeight;
            }
            ctx.restore();
        },

        setTextSize: function(size) {
            if (size) {
                this.textSize = size;
            }
        },

        setTextFont: function(font) {
            if (font) {
                this.textFont = font;
            }
        },

        setTextLineHeight: function(lineHeight) {
            if (lineHeight) {
                this.textLineHeight = lineHeight;
            }
        },

        setPadding: function(padding) {
            if (padding !== undefined) {
                this.padding = padding;
            }
        },

        setTextColor: function(color) {
            if (color) {
                this.textColor = color;
            }
        },

        setTextAlign: function(align) {
            if (align) {
                this.textAlign = align;
            }
        },

        setTextBaseline: function(baseline) {
            if (baseline) {
                this.textBaseline = baseline;
            }
        },

        setTextStroke: function(stroke) {
            if (stroke) {
                this.textStroke = stroke;
            }
        },

        setTextStrokeWidth: function(width) {
            if (width) {
                this.textStrokeWidth = width;
            }
        },

        setContainerHeight: function(height) {
            if (height) {
                this.containerHeight = height;
            }
        },

        measureTextWidth: function(text) {
            var ctx = ig.system.context;
            ctx.font = this.textSize + "px " + this.textFont;
            return ctx.measureText(text).width;
        }
    });
});
