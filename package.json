{"name": "multiplayer-impactjs-marketjs-platform-sandbox", "version": "1.0.0", "description": "IMPACTJS MARKETJS PLATFORM\r ==========================\r ### A cleaner, simpler approach to building HTML5 games", "main": "index.js", "directories": {"doc": "docs", "lib": "lib"}, "dependencies": {"express": "^4.17.1", "nodemon": "^2.0.4", "socket.io": "^2.3.0"}, "devDependencies": {"eslint": "^6.8.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/marketjs_sandbox/impactjs-marketjs-platform-sandbox.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/marketjs_sandbox/impactjs-marketjs-platform-sandbox#readme"}