/* ported from Impact++ */
ig.module(
    'game.utils'
)
.requires(
    'impact.timer',
    'impact.entity'
)
.defines(function () {
    "use strict";

    var _g = ig.global;
    var DURATION_THROTTLE = 500; /* in milliseconds */

    ig.utils = {};

    /**
     * Finds javascript type of object, slower than typeof or instanceof but sometimes necessary.
     * @param {*} o target object
     * @returns {String} Type of object
     */
    ig.utils.type = function (o) {
        return o == null ? o + '' : Object.prototype.toString.call(o).slice(8, -1).toLowerCase();
    };

    /**
     * Checks if target is number.
     * @param {Number} n number
     * @returns {Boolean} true if number, false if not
     */
    ig.utils.isNumber = function (n) {
        return !isNaN(n) && isFinite(n) && typeof n !== 'boolean';
    };

    /**
     * Checks if target is array.
     * @param {*} target target object
     * @returns {Boolean} true if array, false if not
     **/
    ig.utils.isArray = function (target) {
        return Object.prototype.toString.call(target) === '[object Array]';
    };

    /**
     * Ensures an object is an array.
     * @param {*} target target object
     * @returns {Array} Array
     **/
    ig.utils.toArray = function (target) {
        return target ? (ig.utils.isArray(target) !== true ? [target] : target) : [];
    };

    /**
     * Ensures an object is not an array.
     * @param {*} target target object
     * @param {Number} [index=0] index of array to use if target is an array.
     * @returns {*} item at index of array
     **/
    ig.utils.toNotArray = function (target, index) {
        return ig.utils.isArray(target) === true ? target[index || 0] : target;
    };

    /**
     * Add element to target array only if not already in array.
     * <span class="alert"><strong>IMPORTANT:</strong> this modifies the array in place.</span>
     * @param {Array} target Target array
     * @param {*} element Single value to add
     * @returns {Array} Array containing elements
     **/
    ig.utils.arrayCautiousAdd = function (target, element) {
        var index = ig.utils.indexOfValue(target, element);
        if (index === -1) {
            target.push(element);
        }
        return target;
    };

    /**
     * Add elements to target array only if not already in array.
     * <span class="alert"><strong>IMPORTANT:</strong> this modifies the array in place.</span>
     * @param {Array} target Target array
     * @param {*} elements Single object or array of values to add
     * @returns {Array} Array containing elements
     **/
    ig.utils.arrayCautiousAddMulti = function (target, elements) {
        var element, index;
        elements = ig.utils.toArray(elements);
        // for each element
        for (var i = 0, il = elements.length; i < il; i++) {
            element = elements[i];
            if (element !== target) {
                index = ig.utils.indexOfValue(target, element);
                if (index === -1) {
                    target.push(element);
                }
            }
        }
        return target;
    };

    /**
     * Removes element from target array.
     * <span class="alert"><strong>IMPORTANT:</strong> this modifies the array in place.</span>
     * @param {Array} target Target array
     * @param {*} element Single value to remove
     * @returns {Array} Array containing elements
     **/
    ig.utils.arrayCautiousRemove = function (target, element) {
        var index = ig.utils.indexOfValue(target, element);
        if (index !== -1) {
            target.splice(index, 1);
        }
        return target;
    };

    /**
     * Removes elements from target array.
     * <span class="alert"><strong>IMPORTANT:</strong> this modifies the array in place.</span>
     * @param {Array} target Target array
     * @param {*} elements Single object or array of values to remove
     * @returns {Array} Array containing elements
     **/
    ig.utils.arrayCautiousRemoveMulti = function (target, elements) {
        var element, index;
        elements = ig.utils.toArray(elements);
        // for each element
        for (var i = 0, il = elements.length; i < il; i++) {
            element = elements[i];
            if (element !== target) {
                index = ig.utils.indexOfValue(target, element);
                if (index !== -1) {
                    target.splice(index, 1);
                }
            }
        }
        return target;
    };

    /**
     * Executes a callback on each item in an array, in the context of that item.
     * @param {Array} array Array to iterate over
     * @param {Callback} callback Callback to call
     * @param {Array} args Arguments to pass
     **/
    ig.utils.forEach = function (array, callback, args) {
        for (var i = 0, il = array.length; i < il; i++) {
            callback.apply(array[i], args);
        }
    };

    /**
     * Find the index of value in an array.
     * <span class="alert alert-info"><strong>Tip:</strong> in some cases, this may be faster than the native indexOf.</span>
     * @param {Array} array Array to search
     * @param {*} value Value of property to match
     * @returns {Number} >= 0 if found, -1 if not
     **/
    ig.utils.indexOfValue = function (array, value) {
        for (var i = 0, il = array.length; i < il; i++) {
            if (value === array[i]) {
                return i;
            }
        }
        return -1;
    };

    /**
     * Find the index of an object in an array with property = value.
     * @param {Array} array Array to search
     * @param {String} property Property name
     * @param {*} value Value of property to match
     * @returns {Number} >= 0 if found, -1 if not
     **/
    ig.utils.indexOfProperty = function (array, property, value) {
        for (var i = 0, il = array.length; i < il; i++) {
            if (value === array[i][property]) {
                return i;
            }
        }
        return -1;
    };

    /**
     * Find the index of an object in an array matching all property values.
     * @param {Array} array Array to search.
     * @param {Array} properties Property names.
     * @param {Array} values Values to match.
     * @returns {Number} >= 0 if found, -1 if not
     **/
    ig.utils.indexOfProperties = function (array, properties, values) {
        for (var i = 0, il = array.length; i < il; i++) {
            var obj = array[i];
            var missing = false;
            for (var j = 0, jl = properties.length; j < jl; j++) {
                if (values[j] !== obj[properties[j]]) {
                    missing = true;
                    break;
                }
            }
            if (missing !== true) {
                return i;
            }
        }
        return -1;
    };

    /**
     * Throttle a function to execute no more than once per delay (its like a cooldown), based on Ben Alman's jQuery Throttle / Debounce.
     * @param {Function} callback Callback function
     * @param {Number} [delay] Delay in ms
     * @param {Boolean} [trailing] Whether to allow a trailing execution
     * @returns {Function} Throttled function.
     **/
    ig.utils.throttle = function (callback, delay, trailing) {
        var timeoutId;
        var timeLast = 0;

        if (ig.utils.isNumber(delay) !== true) {
            delay = DURATION_THROTTLE;
        }

        function throttled () {
            var me = this;
            var elapsed = Date.now() - timeLast;
            var args = arguments;

            function execute () {
                timeLast = Date.now();
                callback.apply(me, args);
            }

            if (elapsed > delay) {
                timeoutId && _g.clearTimeout(timeoutId);
                execute();
            } else if (trailing !== false) {
                timeoutId && _g.clearTimeout(timeoutId);
                timeoutId = _g.setTimeout(execute, delay - elapsed);
            }
        }

        return throttled;
    };

    /**
     * Debounce a function to execute only once delay reached between subsequent calls, based on Ben Alman's jQuery Throttle / Debounce.
     * @param {Function} callback Callback function
     * @param {Number} [delay] Delay in ms
     * @returns {Function} Debounced function.
     **/
    ig.utils.debounce = function (callback, delay) {
        var timeoutId;

        if (ig.utils.isNumber(delay) !== true) {
            delay = DURATION_THROTTLE;
        }

        function debounced () {
            var me = this;
            var args = arguments;
            timeoutId && _g.clearTimeout(timeoutId);
            timeoutId = _g.setTimeout(function () {
                callback.apply(me, args);
            }, delay);
        }

        return debounced;
    };

    // max value for type flags
    var MAX_TYPE = Math.pow(2, 32);

    /**
     * Gets a type flag by space separated names, and if does not exists, creates it. In ImpactJS, there are 3 default types: A, B, and BOTH. This system allows you to build up to 32 types for much finer control over type, checkAgainst, and group. If you don't understand bitflags, a quick google search will be very helpful!
     *  to avoid poor performance, only use this when initializing an entity
     * IMPORTANT: maxes out at 32 types/flags due to the way Javascript handles bitwise operations.
     * @param {Class} classObject class / object to record types in.
     * @param {String} names space separated names to create type for.
     * @param {String} [typeListName] list name to store types in, defaults to TYPE.
     * @see ig.utils.addType
     **/
    ig.utils.getType = function (classObject, names, typeListName) {
        typeListName = typeListName || 'TYPE';
        var types = classObject[typeListName];
        var typeLastName = typeListName + "_LAST";
        var type;

        // setup types
        if (!classObject[typeLastName] || !types) {
            classObject[typeLastName] = 1;
            types = classObject[typeListName] = {};
        }

        // get type
        names = names.toUpperCase();
        type = types[names];

        // create type
        if (!type) {
            type = 0;
            var typeLast = classObject[typeLastName];
            var namesList = names.split(" ");
            for (var i = 0, il = namesList.length; i < il; i++) {
                var name = namesList[i];
                var typeNext = types[name];
                if (!typeNext) {
                    if (typeLast >= MAX_TYPE) {
                        throw new TypeError('Bitwise flag out of range / above 32 bits!');
                    }
                    // these types are bitwise flags
                    // multiply last type by 2 each time to avoid false positives
                    typeNext = types[name] = typeLast;
                    classObject[typeLastName] = typeLast * 2;
                }
                // add to total type
                type |= typeNext;
            }
            // lets not recalculate that again
            types[names] = type;
        }

        return type;
    };

    /**
     * Adds space separated type flags by name to a property of an entity. In ImpactJS, there are 3 default types: A, B, and BOTH. This system allows you to build up to 32 types for much finer control over type, checkAgainst, and group. If you don't understand bitflags, a quick google search will be very helpful!
     *  to avoid poor performance, only use this when initializing an entity
     * IMPORTANT: maxes out at 32 types/flags due to the way Javascript handles bitwise operations.
     * @param {Class} classObject class object to record types in.
     * @param {ig.EntityExtended} entity entity to add types to.
     * @param {String} property property name within entity to add types to.
     * @param {String} names space separated names to create type for.
     * @param {String} [typeListName] list name to store types in, defaults to TYPE.
     * @example
     *      spawn an entity
     *          var entity = ig.game.spawnEntity( ig.Entity, 0, 0, { ...settings...} );
     *      add a type to our new entity
     *      this type system defaults to using TYPE as the namespace to record unique types
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_NAME" );
     *      when we need to do dynamic checks for that type
     *      refer to it directly using the TYPE namespace
     *          ig.Entity.TYPE.TYPE_NAME
     *      and a check against the type of our new entity will return truthy
     *          entity.type & ig.Entity.TYPE.TYPE_NAME
     *      we can also add a GROUP type to our new entity
     *      note the last argument, we're using the GROUP namespace
     *      this is because group types should be different from TYPE types
     *          ig.utils.addType( ig.Entity, entity, "group", "GROUP_NAME", "GROUP" );
     *      refer to it directly using the GROUP namespace
     *          ig.Entity.GROUP.GROUP_NAME
     *      remember, types and groups can be added together to make combinations!
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_1" );
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_2" );
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_3" );
     *      and we can still check for any one or more of those types easily
     *          entity.type & ig.Entity.TYPE.TYPE_1 === truthy
     *          entity.type & ( ig.Entity.TYPE.TYPE_1 | ig.Entity.TYPE.TYPE_2 ) === truthy
     *          entity.type & ig.Entity.TYPE.TYPE_4 === falsy
     **/
    ig.utils.addType = function (classObject, entity, property, names, typeListName) {
        entity[property] |= ig.utils.getType(classObject, names, typeListName);
    };

    /*
     * Extra properties for ig.Timer.
     */
    ig.Timer.minStep = (1000 / 60) / 1000;
    ig.Timer.overflow = 0;
    ig.Timer.stepped = false;

    // ig.Timer.step = function () {
    //     var current = Date.now();
    //     // add frame time to overflow
    //     // if overflow is above minimum step time
    //     // game should step forward
    //     // this way we can keep a maximum framerate
    //     ig.Timer.overflow += Math.min((current - ig.Timer._last) / 1000, ig.Timer.maxStep);
    //     if (ig.Timer.overflow >= ig.Timer.minStep) {
    //         ig.Timer.overflow -= ig.Timer.minStep;
    //         ig.Timer.time += ig.Timer.minStep * ig.Timer.timeScale;
    //         ig.Timer.stepped = true;
    //     } else {
    //         ig.Timer.stepped = false;
    //     }
    //     ig.Timer._last = current;
    // };

    /**
     * Additional Utility Functions
     */
    ig.utils.pi2 = Math.PI * 2;
    ig.utils.pio2 = Math.PI / 2;

    /**
     * Linearly interpolates between two values.
     * @param {Number} value1 Start value
     * @param {Number} value2 End value
     * @param {Number} amount Interpolation amount [0, 1]
     * @returns {Number} Interpolated value
     */
    ig.utils.lerp = function (value1, value2, amount) {
        amount = ig.utils.clamp(amount, 0, 1);
        return value1 + (value2 - value1) * amount;
    };

    /**
     * Clamps a value between a minimum and maximum.
     * @param {Number} val Value to clamp
     * @param {Number} min Minimum value
     * @param {Number} max Maximum value
     * @returns {Number} Clamped value
     */
    ig.utils.clamp = function (val, min, max) {
        return Math.max(min, Math.min(max, val));
    };

    /**
     * Calculates the distance between two points.
     * @param {Object} p1 First point with x and y properties
     * @param {Object} p2 Second point with x and y properties
     * @returns {Number} Distance between p1 and p2
     */
    ig.utils.distanceBetweenPoints = function (p1, p2) {
        return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));
    };

    /**
     * Calculates the distance between two coordinates.
     * @param {Number} x1 X-coordinate of first point
     * @param {Number} y1 Y-coordinate of first point
     * @param {Number} x2 X-coordinate of second point
     * @param {Number} y2 Y-coordinate of second point
     * @returns {Number} Distance between the two points
     */
    ig.utils.distanceBetween = function (x1, y1, x2, y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    };

    /**
     * Calculates the angle between two points.
     * @param {Object} p1 First point with x and y properties
     * @param {Object} p2 Second point with x and y properties
     * @returns {Number} Angle in radians between p1 and p2
     */
    ig.utils.angleBetweenPoints = function (p1, p2) {
        return Math.atan2(p2.y - p1.y, p2.x - p1.x);
    };

    /**
     * Calculates the angle between two coordinates.
     * @param {Number} x1 X-coordinate of first point
     * @param {Number} y1 Y-coordinate of first point
     * @param {Number} x2 X-coordinate of second point
     * @param {Number} y2 Y-coordinate of second point
     * @returns {Number} Angle in radians between the two points
     */
    ig.utils.angleBetween = function (x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    };

    /**
     * Converts degrees to radians.
     * @param {Number} deg Angle in degrees
     * @returns {Number} Angle in radians
     */
    ig.utils.toRad = function (deg) {
        return deg / 180 * Math.PI;
    };

    /**
     * Converts radians to degrees.
     * @param {Number} rad Angle in radians
     * @returns {Number} Angle in degrees
     */
    ig.utils.toDeg = function (rad) {
        return rad / Math.PI * 180;
    };

    /**
     * Generates a random float between two values.
     * @param {Number} a Minimum value
     * @param {Number} b Maximum value
     * @returns {Number} Random float between a and b
     */
    ig.utils.rBetween = function (a, b) {
        return a + Math.random() * (b - a);
    };

    /**
     * Generates a random integer between two values (inclusive).
     * @param {Number} a Minimum integer
     * @param {Number} b Maximum integer
     * @returns {Number} Random integer between a and b
     */
    ig.utils.iBetween = function (a, b) {
        return a + Math.floor(Math.random() * (b - a + 1));
    };

    /**
     * Determines if a point is contained within a polygon defined by vertices.
     * @param {Array} vertices Array of points (objects with x and y)
     * @param {Object} point Point with x and y properties
     * @returns {Boolean} True if point is inside the polygon, else false
     */
    ig.utils.containPoint = function (vertices, point) {
        var check;
        for (var i = 0; i < vertices.length; i++) {
            var j = i + 1;
            if (j == vertices.length) j = 0;
            var p0 = vertices[i],
                p1 = vertices[j];
            var side = (point.y - p0.y) * (p1.x - p0.x) - (point.x - p0.x) * (p1.y - p0.y);
            if (side == 0) return true;
            if (i === 0) check = side;
            else if (check * side < 0) return false;
        }
        return true;
    };

    /**
     * Picks and removes a random element from an array.
     * @param {Array} arr Array to pick from
     * @returns {*} Removed element
     */
    ig.utils.pick = function (arr) {
        var r = Math.floor(Math.random() * arr.length);
        return arr.splice(r, 1)[0];
    };

    /**
     * Picks a random element from an array without removing it.
     * @param {Array} arr Array to pick from
     * @returns {*} Random element
     */
    ig.utils.randomIn = function (arr) {
        var r = Math.floor(Math.random() * arr.length);
        return arr[r];
    };

    /**
     * Formats seconds into MM:SS format.
     * @param {Number} seconds Total seconds
     * @returns {String} Formatted time string
     */
    ig.utils.toMMSS = function (seconds) {
        // Round down to nearest whole number to avoid decimals
        var wholeSeconds = Math.floor(seconds);

        var minutes = Math.floor(wholeSeconds / 60);
        var remainingSeconds = wholeSeconds % 60;

        // Custom function to pad the number with leading zeros
        function pad (number) {
            return number < 10 ? '0' + number : number;
        }

        return pad(minutes) + ':' + pad(remainingSeconds);
    };

    ig.drawUtil = {
        fontStyle: function (font, color, align) {
            var c = ig.system.context;
            c.font = font;
            c.fillStyle = color;
            c.textAlign = align;
        },
        strokeStyle: function (color, lineWidth) {
            var c = ig.system.context;
            c.strokeStyle = color;
            c.lineWidth = lineWidth;
        },
        fillTextStroke: function (text, x, y) {
            var c = ig.system.context;
            c.strokeText(text, x, y);
            c.fillText(text, x, y);
        },
        clearScreen: function (color) {
            var c = ig.system.context;
            c.fillStyle = color;
            c.clearRect(0, 0, ig.system.width, ig.system.height);
        },
        fillScreen: function (color) {
            var c = ig.system.context;
            c.fillStyle = color;
            c.fillRect(0, 0, ig.system.width, ig.system.height);
        },
        roundRect: function (x, y, w, h, r) {
            var c = ig.system.context;
            c.beginPath();
            c.moveTo(x + r, y);
            c.arcTo(x + w, y, x + w, y + h, r);
            c.arcTo(x + w, y + h, x, y + h, r);
            c.arcTo(x, y + h, x, y, r);
            c.arcTo(x, y, x + w, y, r);
            c.closePath();
            return c;
        }
    };

    /**
     * Logging Utilities
     */
    ig.rlog = {
        debug: window.console.log.bind(window.console, '%c[DEBUG]', 'color: #5393e2;'),
        error: window.console.log.bind(window.console, '%c[ERROR]', 'color: #ee9698;'),
        warn: window.console.log.bind(window.console, '%c[WARN]', 'color: #f9ffb4;'),
        info: window.console.log.bind(window.console, '%c[INFO]', 'color: #d6f6ff;')
    };

    /**
     * Adds a method to CanvasRenderingContext2D to draw rounded rectangles.
     * @param {Number} x X-coordinate
     * @param {Number} y Y-coordinate
     * @param {Number} width Width of the rectangle
     * @param {Number} height Height of the rectangle
     * @param {Number} [radius=5] Radius of the corners
     */
    CanvasRenderingContext2D.prototype.roundRect = function (x, y, width, height, radius) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }
        this.beginPath();
        this.moveTo(x + radius, y);
        this.lineTo(x + width - radius, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.lineTo(x + width, y + height - radius);
        this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.lineTo(x + radius, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.lineTo(x, y + radius);
        this.quadraticCurveTo(x, y, x + radius, y);
        this.closePath();
        this.fill();
    };

    /**
     * Module for generating and rendering text on a canvas.
     */
    ig.TextModule = {
        /**
         * Generates a text canvas based on the provided configurations.
         *
         * @param {Object} configs - Configuration object for the text.
         * @param {string} configs.text - The text to render.
         * @param {string} [configs.fillStyle='black'] - The fill color for the text.
         * @param {string} [configs.textAlign='center'] - The text alignment ('center', 'left', 'right').
         * @param {number} [configs.fontSize=24] - The font size in pixels.
         * @param {string} [configs.fontFamily='Arial'] - The font family.
         * @returns {Object} An object containing the original configurations and the generated canvas.
         * @returns {Object} .configs - The original configuration object.
         * @returns {HTMLCanvasElement} .canvas - The generated canvas element with the rendered text.
         */
        generate: function (configs) {
            var canvas = document.createElement('canvas');
            var context = canvas.getContext('2d');

            var size = this.measure(configs, context);

            // update canvas size
            canvas.width = size.x;
            canvas.height = size.y;

            this.draw(configs, context);

            return {
                configs: configs,
                canvas: canvas
            };
        },

        /**
         * Measures the text metrics for the provided text lines and returns the metrics of the longest line.
         *
         * @param {CanvasRenderingContext2D} ctx - The canvas 2D rendering context.
         * @param {string[]} textLines - An array of text lines to measure.
         * @returns {TextMetrics} The metrics of the longest text line.
         */
        measureLine: function (ctx, textLines) {
            var metrics = ctx.measureText(textLines[0]);
            for (var i = 1; i < textLines.length; i++) {
                var newMetrics = ctx.measureText(textLines[i]);
                if (newMetrics.width >= metrics.width) {
                    metrics = newMetrics;
                }
            }

            return metrics;
        },

        /**
         * Measures the dimensions required to render the text based on the configurations.
         * Handles splitting the text into multiple lines if a line exceeds the maximum width.
         * Includes a feature to split the text manually by using the <br> tag.
         *
         * @param {Object} configs - Configuration object for the text.
         * @param {string} configs.text - The text to measure.
         * @param {number} configs.fontSize - The font size in pixels.
         * @param {string} configs.fontFamily - The font family.
         * @param {CanvasRenderingContext2D} ctx - The canvas 2D rendering context.
         * @returns {Object} The dimensions required to render the text.
         * @returns {number} .x - The width of the text block.
         * @returns {number} .y - The height of the text block.
         */
        measure: function (configs, ctx) {
            var text = configs.text.toString();
            var textLines = text.split("<br>");

            // console.log(textLines)
            ctx.save();
            ctx.font = configs.fontSize + "px" + " " + configs.fontFamily;

            var updatedTextLines = [];
            for (var x = 0; x < textLines.length; x++) {
                var line = textLines[x];
                var lineWidth = ctx.measureText(line).width;

                if (lineWidth > 700) {
                    // Split the line into multiple lines that fit within 700px
                    var words = line.split(" ");
                    var currentLine = words[0];

                    for (var i = 1; i < words.length; i++) {
                        var testLine = currentLine + " " + words[i];
                        var testWidth = ctx.measureText(testLine).width;

                        if (testWidth <= 700) {
                            currentLine = testLine;
                        } else {
                            updatedTextLines.push(currentLine);
                            currentLine = words[i];
                        }
                    }

                    // Add the remaining part of the line
                    updatedTextLines.push(currentLine);
                } else {
                    updatedTextLines.push(line);
                }
            }

            textLines = updatedTextLines;
            var metrics = this.measureLine(ctx, textLines);
            ctx.restore();

            configs.textData = {
                textLines: textLines,
                textWidth: metrics.width,
                fontHeight: metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent
            };
            // Some browsers don't support fontBoundingBoxAscent and fontBoundingBoxDescent
            if (isNaN(configs.textData.fontHeight) || configs.textData.fontHeight == null) {
                configs.textData.fontHeight = configs.fontSize;
            }

            return {
                x: configs.textData.textWidth,
                y: configs.textData.fontHeight * configs.textData.textLines.length
            };
        },

        /**
         * Calculates the position where the text should be drawn on the canvas.
         *
         * @param {Object} configs - Configuration object for the text.
         * @param {CanvasRenderingContext2D} ctx - The canvas 2D rendering context.
         * @returns {Object} The position to draw the text.
         * @returns {number} .x - The x-coordinate for text drawing.
         * @returns {number} .y - The y-coordinate for text drawing.
         */
        getTextDrawPos: function (configs, ctx) {
            var pos = {
                x: ctx.canvas.width * 0.5,
                y: ctx.canvas.height * 0.5
            };

            switch (configs.textAlign) {
                case "start":
                case "left": {
                    pos.x -= configs.textData.textWidth * 0.5;
                } break;

                case "end":
                case "right": {
                    pos.x += configs.textData.textWidth * 0.5;
                } break;

                case "center":
                default: {
                    // No adjustment needed for center alignment
                } break;
            }

            // Offset for multiline text
            if (configs.textData.textLines.length > 1) {
                pos.y -= (configs.textData.textLines.length - 1) * 0.5 * configs.textData.fontHeight;
            }

            return pos;
        },

        /**
         * Draws the text onto the canvas based on the provided configurations.
         *
         * @param {Object} configs - Configuration object for the text.
         * @param {string} configs.fillStyle - The fill color for the text.
         * @param {string} configs.textAlign - The text alignment ('center', 'left', 'right').
         * @param {number} configs.fontSize - The font size in pixels.
         * @param {string} configs.fontFamily - The font family.
         * @param {Object} configs.textData - Additional text data calculated during measurement.
         * @param {string[]} configs.textData.textLines - The array of text lines to render.
         * @param {number} configs.textData.fontHeight - The height of a single line of text.
         * @param {CanvasRenderingContext2D} ctx - The canvas 2D rendering context.
         * @returns {void}
         */
        draw: function (configs, ctx) {
            ctx.save();

            ctx.fillStyle = configs.fillStyle;
            ctx.textAlign = configs.textAlign;
            ctx.textBaseline = "middle";
            ctx.font = configs.fontSize + "px" + " " + configs.fontFamily;

            var drawPos = this.getTextDrawPos(configs, ctx);
            for (var i = 0; i < configs.textData.textLines.length; i++) {
                ctx.fillText(configs.textData.textLines[i],
                    drawPos.x,
                    drawPos.y + i * configs.textData.fontHeight);
            }

            ctx.restore();
        }
    };

    /**
     * Default configuration settings for text rendering.
     *
     * @constant {Object} ig.TextModule.DEFAULT_CONFIGS
     * @property {string} text - The default text (empty string).
     * @property {string} [fillStyle='black'] - The default fill color for the text.
     * @property {string} [textAlign='center'] - The default text alignment ('center', 'left', 'right').
     * @property {number} [fontSize=24] - The default font size in pixels.
     * @property {string} [fontFamily='Arial'] - The default font family.
     */
    ig.TextModule.DEFAULT_CONFIGS = {
        text: "",
        fillStyle: "black",
        textAlign: "center", // [center|left|right];
        fontSize: 24,
        fontFamily: "Arial"
    };


    /**
     * Delays a function call by a certain duration.
     * @param {number} duration Duration in seconds.
     * @param {function} onComplete Function to call when the duration is up.
     * @param {boolean} [autoStart=true] Set to false if you want to start the tween later.
     * @returns {ig.Tween} The tween instance that was created.
     */
    ig.Entity.prototype.delayedCall = function (duration, onComplete, autoStart) {
        if (autoStart === undefined) {
            autoStart = true;
        }
        var tween = new ig.Tween(this, {}, duration, { onComplete: onComplete });
        this.tweens.push(tween);
        if (autoStart) {
            tween.start();
        }
        return tween;
    };

    /** @license
     * JS Signals <http://millermedeiros.github.com/js-signals/>
     * Released under the MIT license
     * Author: Miller Medeiros
     * Version: 1.0.0 - Build: 268 (2012/11/29 05:48 PM)
     */

    // SignalBinding -------------------------------------------------
    //================================================================

    /**
     * Object that represents a binding between a Signal and a listener function.
     * <br />- <strong>This is an internal constructor and shouldn't be called by regular users.</strong>
     * <br />- inspired by Joa Ebert AS3 SignalBinding and Robert Penner's Slot classes.
     * <AUTHOR> Medeiros
     * @constructor
     * @private
     * @name SignalBinding
     * @param {ig.Signal} signal Reference to Signal object that listener is currently bound to.
     * @param {Function} listener Handler function bound to the signal.
     * @param {boolean} isOnce If binding should be executed just once.
     * @param {Object} [listenerContext] Context on which listener will be executed (object that should represent the `this` variable inside listener function).
     * @param {Number} [priority] The priority level of the event listener. (default = 0).
     */
    function SignalBinding (signal, listener, isOnce, listenerContext, priority) {

        /**
         * Handler function bound to the signal.
         * @type Function
         * @private
         */
        this._listener = listener;

        /**
         * If binding should be executed just once.
         * @type boolean
         * @private
         */
        this._isOnce = isOnce;

        /**
         * Context on which listener will be executed (object that should represent the `this` variable inside listener function).
         * @memberOf SignalBinding.prototype
         * @name context
         * @type Object|undefined|null
         */
        this.context = listenerContext;

        /**
         * Reference to Signal object that listener is currently bound to.
         * @type ig.Signal
         * @private
         */
        this._signal = signal;

        /**
         * Listener priority
         * @type Number
         * @private
         */
        this._priority = priority || 0;
    }

    SignalBinding.prototype = {

        /**
         * If binding is active and should be executed.
         * @type boolean
         */
        active: true,

        /**
         * Default parameters passed to listener during `Signal.dispatch` and `SignalBinding.execute`. (curried parameters)
         * @type Array|null
         */
        params: null,

        /**
         * Call listener passing arbitrary parameters.
         * <p>If binding was added using `Signal.addOnce()` it will be automatically removed from signal dispatch queue, this method is used internally for the signal dispatch.</p>
         * @param {Array} [paramsArr] Array of parameters that should be passed to the listener
         * @returns {*} Value returned by the listener.
         */
        execute: function (paramsArr) {
            var handlerReturn, params;
            if (this.active && !! this._listener) {
                params = this.params ? this.params.concat(paramsArr) : paramsArr;
                handlerReturn = this._listener.apply(this.context, params);
                if (this._isOnce) {
                    this.detach();
                }
            }
            return handlerReturn;
        },

        /**
         * Detach binding from signal.
         * - alias to: mySignal.remove(myBinding.getListener());
         * @returns {Function|null} Handler function bound to the signal or `null` if binding was previously detached.
         */
        detach: function () {
            return this.isBound() ? this._signal.remove(this._listener, this.context) : null;
        },

        /**
         * @returns {Boolean} `true` if binding is still bound to the signal and have a listener.
         */
        isBound: function () {
            return ( !! this._signal && !! this._listener);
        },

        /**
         * @returns {boolean} If SignalBinding will only be executed once.
         */
        isOnce: function () {
            return this._isOnce;
        },

        /**
         * @returns {Function} Handler function bound to the signal.
         */
        getListener: function () {
            return this._listener;
        },

        /**
         * @returns {Signal} Signal that listener is currently bound to.
         */
        getSignal: function () {
            return this._signal;
        },

        /**
         * Delete instance properties
         * @private
         */
        _destroy: function () {
            delete this._signal;
            delete this._listener;
            delete this.context;
        },

        /**
         * @returns {string} String representation of the object.
         */
        toString: function () {
            return '[SignalBinding isOnce:' + this._isOnce + ', isBound:' + this.isBound() + ', active:' + this.active + ']';
        }

    };

    // Signal --------------------------------------------------------
    //================================================================

    function validateListener (listener, fnName) {
        if (typeof listener !== 'function') {
            throw new Error('listener is a required param of {fn}() and should be a Function.'.replace('{fn}', fnName));
        }
    }
    /**
     * SignalsJS, a custom event broadcaster, as an Impact module.
     * <br>- converted from Miller Medeiros' SignalJS - http://millermedeiros.github.com/js-signals/
     * @class
     * @memberof ig
     * <AUTHOR> Medeiros - http://millermedeiros.github.com/js-signals/
     */
    ig.Signal = function () {
        /**
         * @type Array.<SignalBinding>
         * @private
         */
        this._bindings = [];
        this._prevParams = null;

        // enforce dispatch to aways work on same context (#47)
        var self = this;
        this.dispatch = function () {
            ig.Signal.prototype.dispatch.apply(self, arguments);
        };
    };

    ig.Signal.prototype = {

        /**
         * Signals Version Number
         * @type String
         * @const
         */
        VERSION: '1.0.0',

        /**
         * If Signal should keep record of previously dispatched parameters and
         * automatically execute listener during `add()`/`addOnce()` if Signal was
         * already dispatched before.
         * @type boolean
         */
        memorize: false,

        /**
         * @type boolean
         * @private
         */
        _shouldPropagate: true,

        /**
         * If Signal is active and should broadcast events.
         * <p><strong>IMPORTANT:</strong> Setting this property during a dispatch will only affect the next dispatch, if you want to stop the propagation of a signal use `halt()` instead.</p>
         * @type boolean
         */
        active: true,

        /**
         * @param {Function} listener
         * @param {boolean} isOnce
         * @param {Object} [listenerContext]
         * @param {Number} [priority]
         * @returns {SignalBinding}
         * @private
         */
        _registerListener: function (listener, isOnce, listenerContext, priority) {

            var prevIndex = this._indexOfListener(listener, listenerContext),
                binding;

            if (prevIndex !== -1) {
                binding = this._bindings[prevIndex];
                if (binding.isOnce() !== isOnce) {
                    throw new Error('You cannot add' + (isOnce ? '' : 'Once') + '() then add' + (!isOnce ? '' : 'Once') + '() the same listener without removing the relationship first.');
                }
            } else {
                binding = new SignalBinding(this, listener, isOnce, listenerContext, priority);
                this._addBinding(binding);
            }

            if (this.memorize && this._prevParams) {
                binding.execute(this._prevParams);
            }

            return binding;
        },

        /**
         * @param {SignalBinding} binding
         * @private
         */
        _addBinding: function (binding) {
            //simplified insertion sort
            var n = this._bindings.length;
            do {
                --n;
            } while (this._bindings[n] && binding._priority <= this._bindings[n]._priority);
            this._bindings.splice(n + 1, 0, binding);
        },

        /**
         * @param {Function} listener
         * @returns {number}
         * @private
         */
        _indexOfListener: function (listener, context) {
            var n = this._bindings.length,
                cur;
            while (n--) {
                cur = this._bindings[n];
                if (cur._listener === listener && cur.context === context) {
                    return n;
                }
            }
            return -1;
        },

        /**
         * Check if listener was attached to Signal.
         * @param {Function} listener
         * @param {Object} [context]
         * @returns {boolean} if Signal has the specified listener.
         */
        has: function (listener, context) {
            return this._indexOfListener(listener, context) !== -1;
        },

        /**
         * Add a listener to the signal.
         * @param {Function} listener Signal handler function.
         * @param {Object} [listenerContext] Context on which listener will be executed (object that should represent the `this` variable inside listener function).
         * @param {Number} [priority] The priority level of the event listener. Listeners with higher priority will be executed before listeners with lower priority. Listeners with same priority level will be executed at the same order as they were added. (default = 0)
         * @returns {SignalBinding} An Object representing the binding between the Signal and listener.
         */
        add: function (listener, listenerContext, priority) {
            validateListener(listener, 'add');
            return this._registerListener(listener, false, listenerContext, priority);
        },

        /**
         * Add listener to the signal that should be removed after first execution (will be executed only once).
         * @param {Function} listener Signal handler function.
         * @param {Object} [listenerContext] Context on which listener will be executed (object that should represent the `this` variable inside listener function).
         * @param {Number} [priority] The priority level of the event listener. Listeners with higher priority will be executed before listeners with lower priority. Listeners with same priority level will be executed at the same order as they were added. (default = 0)
         * @returns {SignalBinding} An Object representing the binding between the Signal and listener.
         */
        addOnce: function (listener, listenerContext, priority) {
            validateListener(listener, 'addOnce');
            return this._registerListener(listener, true, listenerContext, priority);
        },

        /**
         * Remove a single listener from the dispatch queue.
         * @param {Function} listener Handler function that should be removed.
         * @param {Object} [context] Execution context (since you can add the same handler multiple times if executing in a different context).
         * @returns {Function} Listener handler function.
         */
        remove: function (listener, context) {
            validateListener(listener, 'remove');

            var i = this._indexOfListener(listener, context);
            if (i !== -1) {
                this._bindings[i]._destroy(); //no reason to a SignalBinding exist if it isn't attached to a signal
                this._bindings.splice(i, 1);
            }
            return listener;
        },

        /**
         * Remove all listeners from the Signal.
         */
        removeAll: function () {
            var n = this._bindings.length;
            while (n--) {
                this._bindings[n]._destroy();
            }
            this._bindings.length = 0;
        },

        /**
         * @returns {number} Number of listeners attached to the Signal.
         */
        getNumListeners: function () {
            return this._bindings.length;
        },

        /**
         * Stop propagation of the event, blocking the dispatch to next listeners on the queue.
         * <p><strong>IMPORTANT:</strong> should be called only during signal dispatch, calling it before/after dispatch won't affect signal broadcast.</p>
         * @see Signal.prototype.disable
         */
        halt: function () {
            this._shouldPropagate = false;
        },

        /**
         * Dispatch/Broadcast Signal to all listeners added to the queue.
         * @param {...*} [params] Parameters that should be passed to each handler.
         */
        dispatch: function (params) {
            if (!this.active) {
                return;
            }

            var paramsArr = Array.prototype.slice.call(arguments),
                n = this._bindings.length,
                bindings;

            if (this.memorize) {
                this._prevParams = paramsArr;
            }

            if (!n) {
                //should come after memorize
                return;
            }

            bindings = this._bindings.slice(); //clone array in case add/remove items during dispatch
            this._shouldPropagate = true; //in case `halt` was called before dispatch or during the previous dispatch.

            //execute all callbacks until end of the list or until a callback returns `false` or stops propagation
            //reverse loop since listeners with higher priority will be added at the end of the list
            do {
                n--;
            } while (bindings[n] && this._shouldPropagate && bindings[n].execute(paramsArr) !== false);
        },

        /**
         * Forget memorized arguments.
         * @see Signal.memorize
         */
        forget: function () {
            this._prevParams = null;
        },

        /**
         * Remove all bindings from signal and destroy any reference to external objects (destroy Signal object).
         * <p><strong>IMPORTANT:</strong> calling any method on the signal instance after calling dispose will throw errors.</p>
         */
        dispose: function () {
            this.removeAll();
            delete this._bindings;
            delete this._prevParams;
        },

        /**
         * @returns {string} String representation of the object.
         */
        toString: function () {
            return '[Signal active:' + this.active + ' numListeners:' + this.getNumListeners() + ']';
        }

    };

});
