ig.module('game.levels.test-desktop')
    .requires('impact.image', 'game.entities.branding-logo-placeholder', 'game.entities.buttons.button-more-games', 'game.entities.pointer', 'game.entities.buttons.button-sound', 'game.entities.test-control')
    .defines(function () {
        LevelTestDesktop = /* JSON[ */ {
            entities: [{
                type: 'EntityBrandingLogoPlaceholder',
                x: 296,
                y: 396,
                settings: {
                    div_layer_name: 'layer_mainmenu',
                    centralize: 'true'
                }
            }, {
                type: 'EntityButtonMoreGames',
                x: 580,
                y: 284,
                settings: {
                    div_layer_name: 'layer_moregames_mainmenu'
                }
            }, {
                type: 'EntityPointer',
                x: 608,
                y: 120
            }, {
                type: 'EntityButtonSound',
                x: 332,
                y: 284
            }, {
                type: 'EntityTestControl',
                x: 0,
                y: 0
            }],
            layer: [{
                name: 'background',
                width: 16,
                height: 9,
                linkWithCollision: false,
                visible: 1,
                tilesetName: 'media/graphics/backgrounds/desktop/background.jpg',
                repeat: false,
                preRender: true,
                distance: '1',
                tilesize: 60,
                foreground: false,
                data: [
                    [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
                    [17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32],
                    [33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48],
                    [49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
                    [65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80],
                    [81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96],
                    [97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112],
                    [113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128],
                    [129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144]
                ]
            }]
        };
        LevelTestDesktopResources = [new ig.Image('media/graphics/backgrounds/desktop/background.jpg')];
    });
