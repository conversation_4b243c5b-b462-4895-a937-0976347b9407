ig.module(
    'plugins.utils.docker.docker-component'
).requires(
    // No external dependencies - fully self-contained
).defines(function () {
    'use strict';

    /**
     * Docker Component Plugin
     * Repositions entities based on another object's position with percentage and offset support
     *
     * Consolidated from:
     * - lib/dl/game/entity/components/position/docker.js
     * - lib/dl/templates/mixins/docker.js
     *
     * @example:
     *  // Initialize component
     *  this.dockerComponent = new ig.DockerComponent(this);
     *  this.dockerComponent.init(config);
     *
     *  // Configure in class
     *  this.dockerComponent.updateProperties({
     *      dockerObject: dockerObject,
     *      dockerPercent: { x: 0.5, y: 0.5 },
     *      dockerOffset: { x: 0, y: 0 }
     *  });
     */

    ig.DockerComponent = ig.Class.extend({
        init: function (entity) {
            this.entity = entity;
            this.enable = true;

            // Bound event handler for position updates
            this.boundUpdatePos = this.updatePos.bind(this);

            // Docker object property with event binding
            this._dockerObject = null;
            Object.defineProperty(this, 'dockerObject', {
                get: function () {
                    return this._dockerObject;
                },
                set: function (v) {
                    // Unbind from previous docker object
                    if (this._isDefined(this._dockerObject)) {
                        if (this._isFunction(this._dockerObject.offEvent)) {
                            this._dockerObject.offEvent('positionChanged', this.boundUpdatePos);
                        }
                    }

                    this._dockerObject = v;

                    // Update position immediately
                    this.updatePos();

                    // Bind to new docker object
                    if (this._isDefined(this._dockerObject)) {
                        if (this._isFunction(this._dockerObject.onEvent)) {
                            this._dockerObject.onEvent('positionChanged', this.boundUpdatePos);
                        }
                    }
                }
            });

            // Default positioning configuration
            this.dockerPercent = { x: 0.5, y: 0.5 };  // Center by default
            this.dockerOffset = { x: 0, y: 0 };       // No offset by default

            // Calculated position
            this.pos = { x: 0, y: 0 };

            // Override entity positioning
            this.overridingEntity();
        },

        // Utility functions to replace dl.check dependencies
        _isDefined: function (value) {
            return typeof value !== 'undefined' && value !== null;
        },

        _isFunction: function (value) {
            return typeof value === 'function';
        },

        _isEmptyObject: function (obj) {
            return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        // Initialize component with settings
        initWithSettings: function (settings) {
            this.updateProperties(settings || {});
        },

        // Update component properties
        updateProperties: function (properties) {
            if (this._isEmptyObject(properties)) return;

            // Use Impact.js merge function if available, otherwise manual merge
            if (typeof ig.merge === 'function') {
                ig.merge(this, properties);
            } else {
                for (var key in properties) {
                    if (properties.hasOwnProperty(key)) {
                        this[key] = properties[key];
                    }
                }
            }
            this.onPropertiesChanged();
        },

        onPropertiesChanged: function () {
            this.updatePos();
        },

        /**
         * Override entity's updateBasePos method to use docker positioning
         */
        overridingEntity: function () {
            // Store original updateBasePos method
            this.bound_Entity_UpdateBasePos = this.entity.updateBasePos.bind(this.entity);
            
            // Override with docker positioning logic
            this.entity.updateBasePos = function () {
                if (this.updateDockerPos()) {
                    // Use docker position
                    this.entity.pos.x = this.pos.x;
                    this.entity.pos.y = this.pos.y;
                } else {
                    // Fall back to original positioning
                    this.bound_Entity_UpdateBasePos();
                }
            }.bind(this);
        },

        /**
         * Trigger entity position update
         */
        updatePos: function () {
            this.entity.updatePos();
        },

        /**
         * Calculate and update docker position
         * @returns {boolean} true if docker positioning was applied, false otherwise
         */
        updateDockerPos: function () {
            // Check if component is enabled and docker object is defined
            if (!this.enable) return false;
            if (!this._isDefined(this.dockerObject)) return false;

            // Calculate position based on docker object
            this.pos.x = this.dockerObject.pos.x +
                this.dockerObject.size.x * (this.dockerPercent.x - 0.5) +
                this.dockerOffset.x;

            this.pos.y = this.dockerObject.pos.y +
                this.dockerObject.size.y * (this.dockerPercent.y - 0.5) +
                this.dockerOffset.y;

            return true;
        },

        /**
         * Set docker object with optional configuration
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} options - Optional configuration
         * @param {Object} options.percent - Docker percentage {x, y}
         * @param {Object} options.offset - Docker offset {x, y}
         */
        setDockerObject: function (dockerObject, options) {
            options = options || {};
            
            if (options.percent) {
                this.dockerPercent = options.percent;
            }
            
            if (options.offset) {
                this.dockerOffset = options.offset;
            }
            
            this.dockerObject = dockerObject;
        },

        /**
         * Dock to camera with optional positioning
         * @param {Object} options - Optional configuration
         * @param {Object} options.percent - Docker percentage {x, y}
         * @param {Object} options.offset - Docker offset {x, y}
         */
        dockToCamera: function (options) {
            this.setDockerObject(ig.game.camera || dl.game.camera, options);
        },

        /**
         * Clean up event bindings when component is destroyed
         */
        destroy: function () {
            if (this._isDefined(this._dockerObject)) {
                if (this._isFunction(this._dockerObject.offEvent)) {
                    this._dockerObject.offEvent('positionChanged', this.boundUpdatePos);
                }
            }

            // Restore original entity method if it exists
            if (this.bound_Entity_UpdateBasePos) {
                this.entity.updateBasePos = this.bound_Entity_UpdateBasePos;
            }
        }
    });
});
