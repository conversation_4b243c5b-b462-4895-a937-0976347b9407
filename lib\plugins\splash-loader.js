ig.module('plugins.splash-loader')
.requires(
    'impact.loader',
    'impact.animation'
)
.defines(function () {
    ig.SplashLoader = ig.Loader.extend({
        tapToStartDivId: "tap-to-start",
        // bgImage: new ig.Image('media/graphics/sprites/ui/background-plain.png'),
        // bgImageDetailed: new ig.Image('media/graphics/sprites/ui/background-detailed.png'),
        // titleImage: new ig.Image('media/graphics/sprites/ui/title.png'),
        loadingBarImage: new ig.Image('media/graphics/sprites/ui/etc/progress-bg.png'),
        loadingBarFillImage: new ig.Image('media/graphics/sprites/ui/etc/progress-fill.png'),

        loadingTimer: null,
        loadingComplete: false,
        tweenValue: 0,
        startTween: false,
        tweenSpeed: 0.01,
        init: function (gameClass, resources){
            this.parent(gameClass, resources);
            window.splashloader = this;
            // ADS
            ig.apiHandler.run("MJSPreroll");
            this.loadingTimer = new ig.Timer();
            this.repos();
        },

        end: function (){
            this._endParent = this.parent;
			this._drawStatus = 1;
            this.loadingComplete = true;
            this.startTween = true;
            
            this.draw();
        },

        setGame: function () {
            if (_SETTINGS['TapToStartAudioUnlock']['Enabled']) {
                this.tapToStartDiv(function () {
                    /* play game */
                    this._endParent();
                    if (typeof (ig.game) === 'undefined' || ig.game == null) {
						ig.system.setGame( this.gameClass );
					}
                }.bind(this));
            }
            else {
                /* play game */
                this._endParent();
                if (typeof (ig.game) === 'undefined' || ig.game == null) {
                    ig.system.setGame( this.gameClass );
                }
            }
        },
        
        tapToStartDiv: function ( onClickCallbackFunction ){
            this.desktopCoverDIV = document.getElementById(this.tapToStartDivId);
            
            // singleton pattern
            if ( this.desktopCoverDIV ) {
                return;
            }
            
            /* create DIV */
            this.desktopCoverDIV = document.createElement("div");
            this.desktopCoverDIV.id = this.tapToStartDivId;
            this.desktopCoverDIV.setAttribute("class", "play");
            this.desktopCoverDIV.setAttribute("style", "position: absolute; display: block; z-index: 999999; background-color: rgba(23, 32, 53, 0.7); visibility: visible; font-size: 10vmin; text-align: center; vertical-align: middle; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;");
            this.desktopCoverDIV.innerHTML = "<div style='color:white;background-color: rgba(255, 255, 255, 0.3); border: 2px solid #fff; font-size:20px; border-radius: 5px; position: relative; float: left; top: 50%; left: 50%; transform: translate(-50%, -50%);'><div style='padding:20px 50px; font-family: luckiestguy-regular;'>" + _STRINGS["Splash"]["TapToStart"] + "</div></div>";
            
            
            /* inject DIV */
            var parentDIV = document.getElementById("play").parentNode || document.getElementById("ajaxbar");
            parentDIV.appendChild(this.desktopCoverDIV);
            
            /* reize DIV */
            try {
                if ( typeof (ig.sizeHandler) !== "undefined" ) {
                    if ( typeof (ig.sizeHandler.coreDivsToResize) !== "undefined" ) {
                        ig.sizeHandler.coreDivsToResize.push( ("#" + this.tapToStartDivId) );
                        if ( typeof (ig.sizeHandler.reorient) === "function" ) {
                            ig.sizeHandler.reorient();
                        }
                    }
                }
                else if ( typeof (coreDivsToResize) !== "undefined" ) {
                    coreDivsToResize.push(this.tapToStartDivId);
                    if ( typeof (sizeHandler) === "function" ) {
                        sizeHandler();
                    }
                }
            } catch (error) {
                console.log(error);
            }
            
            
            /* click DIV */
            this.desktopCoverDIV.addEventListener("click", function (){
                ig.soundHandler.unlockWebAudio();
            
                /* hide DIV */
                this.setAttribute("style", "visibility: hidden;");
            
                /* end function */
                if ( typeof (onClickCallbackFunction) === "function" ) {
                    onClickCallbackFunction();
                }
            });
        },

        drawCheck: 0,
        draw: function () {
            this.repos();
            var ctx = ig.system.context;
            this._drawStatus += (this.status - this._drawStatus) / 5;
            
            //Check the game screen. see if the font are loaded first. Removing the two lines below is safe :)
            if (this.drawCheck === 1) console.log('Font should be loaded before loader draw loop');
            if (this.drawCheck < 2) this.drawCheck ++;
            
            if (this.bgImage) this.drawBG();

            ctx.save();

            if (this.titleImage) {
                // draw title image above loading bar
                var titleW = this.titleImage.width;
                var titleH = this.titleImage.height;
                var titleX = ig.system.width * 0.5 - titleW * 0.5;
                var titleY = ig.system.height * 0.5 - titleH * 0.75;
                this.titleImage.draw(titleX, titleY, 0, 0, titleW, titleH);
            }
            
            if (!this.loadingComplete) {
                return;  // Skip drawing if loading is not yet complete
            }
            ig.Timer.step();
            if (this.startTween && this.tweenValue >= 1) {
                this.startTween = false;
                this.setGame();
            }
            if (this.startTween && this.tweenValue < 1) {
                this.tweenValue += this.loadingTimer.delta() * 0.05;  // Adjust the multiplier to control speed
                this.tweenValue = Math.min(this.tweenValue, 1);  // Ensure it does not exceed 1
            }
            
            // draw loading bar under the title
            var barW = this.loadingBarImage.width;
            var barH = this.loadingBarImage.height;
            var barX = ig.system.width * 0.5 - barW * 0.5;
            if (this.titleImage) {
                var barY = titleY + titleH - barH * 0.5 + 120;
            } else {
                var barY = ig.system.height * 0.5 - barH * 0.5 + 120;
            }
            this.loadingBarImage.draw(barX, barY, 0, 0, barW, barH);
            // draw loading bar fill
            var fillW = this.loadingBarFillImage.width * this.tweenValue;
            var fillH = this.loadingBarFillImage.height;
            this.loadingBarFillImage.draw(barX, barY, 0, 0, fillW, fillH);
            ctx.restore();

            this.drawVersion();
        },

        drawBG: function () {
            var tileWidth = this.bgImage.width;
            var tileHeight = this.bgImage.height;
        
            var centerX = ig.system.width * 0.5;
            var centerY = ig.system.height * 0.5;
        
            var tilesX = Math.ceil(centerX / tileWidth) + 1;
            var tilesY = Math.ceil(centerY / tileHeight) + 1;
        
            for (var i = -tilesX; i <= tilesX; i++) {
                for (var j = -tilesY; j <= tilesY; j++) {
                    var image = (j === 0) ? this.bgImageDetailed : this.bgImage;
                    var drawX = centerX + i * tileWidth - tileWidth * 0.5;
                    var drawY = centerY + j * tileHeight - tileHeight * 0.5;
                    
                    // Ensure we're drawing within the canvas
                    if (drawX < ig.system.width && drawX + tileWidth > 0 &&
                        drawY < ig.system.height && drawY + tileHeight > 0) {
                        image.draw(ig.system.getDrawPos(drawX), ig.system.getDrawPos(drawY));
                    }
                }
            }
        },

        drawVersion: function () {
			if (typeof(_SETTINGS.Versioning) !== "undefined" && _SETTINGS.Versioning !== null) {
                if (_SETTINGS.Versioning.DrawVersion) {
                    var ctx = ig.system.context;
					fontSize = _SETTINGS.Versioning.FontSize,
					fontFamily = _SETTINGS.Versioning.FontFamily,
					fillStyle = _SETTINGS.Versioning.FillStyle;

					ctx.save();
					ctx.textBaseline = "bottom";
					ctx.textAlign = "left";
					ctx.font = fontSize + " " + fontFamily || "10px Arial";
					ctx.fillStyle = fillStyle || '#ffffff';
					ctx.fillText("v" + _SETTINGS.Versioning.Version + "+build." + _SETTINGS.Versioning.Build, 10, ig.system.height - 10);
					ctx.restore();
                }
			}
		},

        repos: function () {
            if (this.bgImage) {
                var r1 = this.bgImage.width / this.bgImage.height,
                    r2 = ig.system.width / ig.system.height;
                if (r1 > r2) {
                    this.bgH = this.bgImage.height;
                    this.bgW = this.bgH * r2;
                    this.bgX = (this.bgImage.width - this.bgW) * 0.5;
                    this.bgY = 0;
                } else {
                    this.bgW = this.bgImage.width;
                    this.bgH = this.bgW / r2;
                    this.bgX = 0;
                    this.bgY = (this.bgImage.height - this.bgH) * 0.5;
                }
            }
        }
    });
});
