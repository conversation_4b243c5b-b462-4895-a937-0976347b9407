/**
 * Responsive Plugin by Nam
 */
ig.module('plugins.responsive')
    .requires(
        'plugins.handlers.size-handler'
    ).defines(function () {

        ig.SizeHandler.inject({
            minW: 1080,
            minH: 1920,
            firstResize: true,
            isLandscape: false,
            sizeCalcs: function () {
                var w = window.innerWidth,
                    h = window.innerHeight,
                    newW, newH;
                if (h > w) {
                    // Portrait
                    this.isLandscape = false;
                    // this.minW = 1080;
                    // this.minH = 1920;
                } else {
                    // Landscape
                    this.isLandscape = true;
                    // this.minW = 1920;
                    // this.minH = 1080;
                }
                this.r0 = this.minW / this.minH;
                var r = w / h;
                /* Calculate new game size */
                if (r < this.r0) {
                    newW = this.minW;
                    newH = Math.round(newW / r);
                } else {
                    newH = this.minH;
                    newW = Math.round(newH * r);
                }

                if (ig.system) {
                    this.dx = (ig.system.width - newW) / 2;
                    this.dy = (ig.system.height - newH) / 2;
                    ig.system.resize(newW, newH, this.scale);
                }

                this.windowSize = new Vector2(w, h);
                this.scaleRatioMultiplier = new Vector2(w / newW, h / newH);
                this.desktop.actualResolution = new Vector2(w, h);
                this.mobile.actualResolution = new Vector2(w, h);
                this.desktop.actualSize = new Vector2(w, h);
                this.mobile.actualSize = new Vector2(w, h);

                if (ig.game) {
                    ig.game.midX = ig.system.width / 2;
                    ig.game.midY = ig.system.height / 2;

                    var dx2 = (this.minW - newW) / 2;
                    var dy2 = (this.minH - newH) / 2;

                    ig.game.screen.x = dx2;
                    ig.game.screen.y = dy2;

                    ig.game.update();

                    /* Reposition entities */
                    this.repositionEntities();

                    /* Update drawing */
                    ig.game.draw();

                } else if (ig.loader) {
                    ig.loader.draw(); // update splash-loader screen
                }
            },

            /* Reposition entities */
            repositionEntities: function () {
                ig.game.entities.forEach(function (e) {
                    e && typeof e.repos === 'function' && e.repos();
                });
            },

            /* OVERRIDE METHOD, TO GET RID OF THE ORIENTATION IMAGE */
            reorient: function () {
                this.resize();
                ig.ua.mobile && this.resizeAds();
            }
        });
    });