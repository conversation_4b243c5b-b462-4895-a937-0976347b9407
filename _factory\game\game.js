/*! jQuery v3.2.1 | (c) JS Foundation and other contributors | jquery.org/license */ function _instanceof(left, right) {
    if (right != null && typeof Symbol !== "undefined" && right[Symbol.hasInstance]) {
        return !!right[Symbol.hasInstance](left);
    } else {
        return left instanceof right;
    }
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
!function(a, b) {
    'use strict';
    (typeof module === "undefined" ? "undefined" : _type_of(module)) == 'object' && _type_of(module.exports) == 'object' ? module.exports = a.document ? b(a, !0) : function(a) {
        if (!a.document) throw new Error('jQuery requires a window with a document');
        return b(a);
    } : b(a);
}(typeof window != 'undefined' ? window : this, function(a, b) {
    'use strict';
    var c = [];
    var d = a.document;
    var e = Object.getPrototypeOf;
    var f = c.slice;
    var g = c.concat;
    var h = c.push;
    var i = c.indexOf;
    var j = {};
    var k = j.toString;
    var l = j.hasOwnProperty;
    var m = l.toString;
    var n = m.call(Object);
    var o = {};
    function p(a, b) {
        b = b || d;
        var c = b.createElement('script');
        c.text = a, b.head.appendChild(c).parentNode.removeChild(c);
    }
    var q = '3.2.1';
    var r = function r1(a, b) {
        return new r.fn.init(a, b);
    };
    var s = /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;
    var t = /^-ms-/;
    var u = /-([a-z])/g;
    var v = function v(a, b) {
        return b.toUpperCase();
    };
    r.fn = r.prototype = {
        jquery: q,
        constructor: r,
        length: 0,
        toArray: function toArray() {
            return f.call(this);
        },
        get: function get(a) {
            return a == null ? f.call(this) : a < 0 ? this[a + this.length] : this[a];
        },
        pushStack: function pushStack(a) {
            var _$b = r.merge(this.constructor(), a);
            return _$b.prevObject = this, _$b;
        },
        each: function each(a) {
            return r.each(this, a);
        },
        map: function map(a) {
            return this.pushStack(r.map(this, function(b, c) {
                return a.call(b, c, b);
            }));
        },
        slice: function slice() {
            return this.pushStack(f.apply(this, arguments));
        },
        first: function first() {
            return this.eq(0);
        },
        last: function last() {
            return this.eq(-1);
        },
        eq: function eq(a) {
            var _$b = this.length;
            var c = +a + (a < 0 ? _$b : 0);
            return this.pushStack(c >= 0 && c < _$b ? [
                this[c]
            ] : []);
        },
        end: function end() {
            return this.prevObject || this.constructor();
        },
        push: h,
        sort: c.sort,
        splice: c.splice
    }, r.extend = r.fn.extend = function() {
        var _$a;
        var _$b;
        var c;
        var d;
        var e;
        var f;
        var g = arguments[0] || {};
        var h = 1;
        var i = arguments.length;
        var j = !1;
        for(typeof g == 'boolean' && (j = g, g = arguments[h] || {}, h++), (typeof g === "undefined" ? "undefined" : _type_of(g)) == 'object' || r.isFunction(g) || (g = {}), h === i && (g = this, h--); h < i; h++)if ((_$a = arguments[h]) != null) for(_$b in _$a)c = g[_$b], d = _$a[_$b], g !== d && (j && d && (r.isPlainObject(d) || (e = Array.isArray(d))) ? (e ? (e = !1, f = c && Array.isArray(c) ? c : []) : f = c && r.isPlainObject(c) ? c : {}, g[_$b] = r.extend(j, f, d)) : void 0 !== d && (g[_$b] = d));
        return g;
    }, r.extend({
        expando: 'jQuery' + (q + Math.random()).replace(/\D/g, ''),
        isReady: !0,
        error: function error(a) {
            throw new Error(a);
        },
        noop: function noop() {},
        isFunction: function isFunction(a) {
            return r.type(a) === 'function';
        },
        isWindow: function isWindow(a) {
            return a != null && a === a.window;
        },
        isNumeric: function isNumeric(a) {
            var _$b = r.type(a);
            return (_$b === 'number' || _$b === 'string') && !isNaN(a - parseFloat(a));
        },
        isPlainObject: function isPlainObject(a) {
            var _$b, c;
            return !(!a || k.call(a) !== '[object Object]') && (!(_$b = e(a)) || (c = l.call(_$b, 'constructor') && _$b.constructor, typeof c == 'function' && m.call(c) === n));
        },
        isEmptyObject: function isEmptyObject(a) {
            var _$b;
            for(_$b in a)return !1;
            return !0;
        },
        type: function type(a) {
            return a == null ? a + '' : (typeof a === "undefined" ? "undefined" : _type_of(a)) == 'object' || typeof a == 'function' ? j[k.call(a)] || 'object' : typeof a === "undefined" ? "undefined" : _type_of(a);
        },
        globalEval: function globalEval(a) {
            p(a);
        },
        camelCase: function camelCase(a) {
            return a.replace(t, 'ms-').replace(u, v);
        },
        each: function each(a, b) {
            var c;
            var d = 0;
            if (w(a)) {
                for(c = a.length; d < c; d++)if (b.call(a[d], d, a[d]) === !1) break;
            } else for(d in a)if (b.call(a[d], d, a[d]) === !1) break;
            return a;
        },
        trim: function trim(a) {
            return a == null ? '' : (a + '').replace(s, '');
        },
        makeArray: function makeArray(a, b) {
            var c = b || [];
            return a != null && (w(Object(a)) ? r.merge(c, typeof a == 'string' ? [
                a
            ] : a) : h.call(c, a)), c;
        },
        inArray: function inArray(a, b, c) {
            return b == null ? -1 : i.call(b, a, c);
        },
        merge: function merge(a, b) {
            for(var c = +b.length, d = 0, e = a.length; d < c; d++)a[e++] = b[d];
            return a.length = e, a;
        },
        grep: function grep(a, b, c) {
            for(var d, e = [], f = 0, g = a.length, h = !c; f < g; f++)d = !b(a[f], f), d !== h && e.push(a[f]);
            return e;
        },
        map: function map(a, b, c) {
            var d;
            var e;
            var f = 0;
            var h = [];
            if (w(a)) for(d = a.length; f < d; f++)e = b(a[f], f, c), e != null && h.push(e);
            else for(f in a)e = b(a[f], f, c), e != null && h.push(e);
            return g.apply([], h);
        },
        guid: 1,
        proxy: function proxy(a, b) {
            var c, d, e;
            if (typeof b == 'string' && (c = a[b], b = a, a = c), r.isFunction(a)) return d = f.call(arguments, 2), e = function e() {
                return a.apply(b || this, d.concat(f.call(arguments)));
            }, e.guid = a.guid = a.guid || r.guid++, e;
        },
        now: Date.now,
        support: o
    }), typeof Symbol == 'function' && (r.fn[Symbol.iterator] = c[Symbol.iterator]), r.each('Boolean Number String Function Array Date RegExp Object Error Symbol'.split(' '), function(a, b) {
        j['[object ' + b + ']'] = b.toLowerCase();
    });
    function w(a) {
        var _$b = !!a && 'length' in a && a.length;
        var c = r.type(a);
        return c !== 'function' && !r.isWindow(a) && (c === 'array' || _$b === 0 || typeof _$b == 'number' && _$b > 0 && _$b - 1 in a);
    }
    var x = function(a) {
        var _$b;
        var c;
        var d;
        var e;
        var f;
        var g;
        var h;
        var i;
        var j;
        var k;
        var l;
        var m;
        var n;
        var o;
        var p;
        var q;
        var r;
        var s;
        var t;
        var u = 'sizzle' + 1 * new Date();
        var v = a.document;
        var w = 0;
        var x = 0;
        var y = ha();
        var z = ha();
        var A = ha();
        var _$B = function B(a, b) {
            return a === b && (l = !0), 0;
        };
        var C = {}.hasOwnProperty;
        var D = [];
        var E = D.pop;
        var F = D.push;
        var G = D.push;
        var H = D.slice;
        var I = function I(a, b) {
            for(var c = 0, d = a.length; c < d; c++)if (a[c] === b) return c;
            return -1;
        };
        var J = 'checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped';
        var K = '[\\x20\\t\\r\\n\\f]';
        var L = '(?:\\\\.|[\\w-]|[^\0-\\xa0])+';
        var M = '\\[' + K + '*(' + L + ')(?:' + K + '*([*^$|!~]?=)' + K + "*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|(" + L + '))|)' + K + '*\\]';
        var N = ':(' + L + ")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|" + M + ')*)|.*)\\)|)';
        var O = new RegExp(K + '+', 'g');
        var P = new RegExp('^' + K + '+|((?:^|[^\\\\])(?:\\\\.)*)' + K + '+$', 'g');
        var Q = new RegExp('^' + K + '*,' + K + '*');
        var R = new RegExp('^' + K + '*([>+~]|' + K + ')' + K + '*');
        var _$S = new RegExp('=' + K + "*([^\\]'\"]*?)" + K + '*\\]', 'g');
        var T = new RegExp(N);
        var U = new RegExp('^' + L + '$');
        var _$V = {
            ID: new RegExp('^#(' + L + ')'),
            CLASS: new RegExp('^\\.(' + L + ')'),
            TAG: new RegExp('^(' + L + '|[*])'),
            ATTR: new RegExp('^' + M),
            PSEUDO: new RegExp('^' + N),
            CHILD: new RegExp('^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\(' + K + '*(even|odd|(([+-]|)(\\d*)n|)' + K + '*(?:([+-]|)' + K + '*(\\d+)|))' + K + '*\\)|)', 'i'),
            bool: new RegExp('^(?:' + J + ')$', 'i'),
            needsContext: new RegExp('^' + K + '*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\(' + K + '*((?:-\\d)?\\d*)' + K + '*\\)|)(?=[^-]|$)', 'i')
        };
        var W = /^(?:input|select|textarea|button)$/i;
        var X = /^h\d$/i;
        var Y = /^[^{]+\{\s*\[native \w/;
        var Z = /^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/;
        var $ = /[+~]/;
        var _ = new RegExp('\\\\([\\da-f]{1,6}' + K + '?|(' + K + ')|.)', 'ig');
        var aa = function aa(a, b, c) {
            var d = '0x' + b - 65536;
            return d !== d || c ? b : d < 0 ? String.fromCharCode(d + 65536) : String.fromCharCode(d >> 10 | 55296, 1023 & d | 56320);
        };
        var ba = /([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g;
        var ca = function ca(a, b) {
            return b ? a === '\0' ? '\ufffd' : a.slice(0, -1) + '\\' + a.charCodeAt(a.length - 1).toString(16) + ' ' : '\\' + a;
        };
        var da = function da() {
            m();
        };
        var ea = ta(function(a) {
            return a.disabled === !0 && ('form' in a || 'label' in a);
        }, {
            dir: 'parentNode',
            next: 'legend'
        });
        try {
            G.apply(D = H.call(v.childNodes), v.childNodes), D[v.childNodes.length].nodeType;
        } catch (fa) {
            G = {
                apply: D.length ? function(a, b) {
                    F.apply(a, H.call(b));
                } : function(a, b) {
                    var c = a.length;
                    var d = 0;
                    while(a[c++] = b[d++]);
                    a.length = c - 1;
                }
            };
        }
        function ga(a, b, d, e) {
            var f;
            var h;
            var j;
            var k;
            var l;
            var o;
            var r;
            var s = b && b.ownerDocument;
            var w = b ? b.nodeType : 9;
            if (d = d || [], typeof a != 'string' || !a || w !== 1 && w !== 9 && w !== 11) return d;
            if (!e && ((b ? b.ownerDocument || b : v) !== n && m(b), b = b || n, p)) {
                if (w !== 11 && (l = Z.exec(a))) if (f = l[1]) {
                    if (w === 9) {
                        if (!(j = b.getElementById(f))) return d;
                        if (j.id === f) return d.push(j), d;
                    } else if (s && (j = s.getElementById(f)) && t(b, j) && j.id === f) return d.push(j), d;
                } else {
                    if (l[2]) return G.apply(d, b.getElementsByTagName(a)), d;
                    if ((f = l[3]) && c.getElementsByClassName && b.getElementsByClassName) return G.apply(d, b.getElementsByClassName(f)), d;
                }
                if (c.qsa && !A[a + ' '] && (!q || !q.test(a))) {
                    if (w !== 1) s = b, r = a;
                    else if (b.nodeName.toLowerCase() !== 'object') {
                        (k = b.getAttribute('id')) ? k = k.replace(ba, ca) : b.setAttribute('id', k = u), o = g(a), h = o.length;
                        while(h--)o[h] = '#' + k + ' ' + sa(o[h]);
                        r = o.join(','), s = $.test(a) && qa(b.parentNode) || b;
                    }
                    if (r) try {
                        return G.apply(d, s.querySelectorAll(r)), d;
                    } catch (x) {} finally{
                        k === u && b.removeAttribute('id');
                    }
                }
            }
            return i(a.replace(P, '$1'), b, d, e);
        }
        function ha() {
            var _$a = [];
            function b(c, e) {
                return _$a.push(c + ' ') > d.cacheLength && delete b[_$a.shift()], b[c + ' '] = e;
            }
            return b;
        }
        function ia(a) {
            return a[u] = !0, a;
        }
        function ja(a) {
            var _$b = n.createElement('fieldset');
            try {
                return !!a(_$b);
            } catch (c) {
                return !1;
            } finally{
                _$b.parentNode && _$b.parentNode.removeChild(_$b), _$b = null;
            }
        }
        function ka(a, b) {
            var c = a.split('|');
            var e = c.length;
            while(e--)d.attrHandle[c[e]] = b;
        }
        function la(a, b) {
            var c = b && a;
            var d = c && a.nodeType === 1 && b.nodeType === 1 && a.sourceIndex - b.sourceIndex;
            if (d) return d;
            if (c) {
                while(c = c.nextSibling)if (c === b) return -1;
            }
            return a ? 1 : -1;
        }
        function ma(a) {
            return function(b) {
                var c = b.nodeName.toLowerCase();
                return c === 'input' && b.type === a;
            };
        }
        function na(a) {
            return function(b) {
                var c = b.nodeName.toLowerCase();
                return (c === 'input' || c === 'button') && b.type === a;
            };
        }
        function oa(a) {
            return function(b) {
                return 'form' in b ? b.parentNode && b.disabled === !1 ? 'label' in b ? 'label' in b.parentNode ? b.parentNode.disabled === a : b.disabled === a : b.isDisabled === a || b.isDisabled !== !a && ea(b) === a : b.disabled === a : 'label' in b && b.disabled === a;
            };
        }
        function pa(a) {
            return ia(function(b) {
                return b = +b, ia(function(c, d) {
                    var e;
                    var f = a([], c.length, b);
                    var g = f.length;
                    while(g--)c[e = f[g]] && (c[e] = !(d[e] = c[e]));
                });
            });
        }
        function qa(a) {
            return a && typeof a.getElementsByTagName != 'undefined' && a;
        }
        c = ga.support = {}, f = ga.isXML = function f(a) {
            var _$b = a && (a.ownerDocument || a).documentElement;
            return !!_$b && _$b.nodeName !== 'HTML';
        }, m = ga.setDocument = function m(a) {
            var _$b;
            var e;
            var g = a ? a.ownerDocument || a : v;
            return g !== n && g.nodeType === 9 && g.documentElement ? (n = g, o = n.documentElement, p = !f(n), v !== n && (e = n.defaultView) && e.top !== e && (e.addEventListener ? e.addEventListener('unload', da, !1) : e.attachEvent && e.attachEvent('onunload', da)), c.attributes = ja(function(a) {
                return a.className = 'i', !a.getAttribute('className');
            }), c.getElementsByTagName = ja(function(a) {
                return a.appendChild(n.createComment('')), !a.getElementsByTagName('*').length;
            }), c.getElementsByClassName = Y.test(n.getElementsByClassName), c.getById = ja(function(a) {
                return o.appendChild(a).id = u, !n.getElementsByName || !n.getElementsByName(u).length;
            }), c.getById ? (d.filter.ID = function(a) {
                var _$b = a.replace(_, aa);
                return function(a) {
                    return a.getAttribute('id') === _$b;
                };
            }, d.find.ID = function(a, b) {
                if (typeof b.getElementById != 'undefined' && p) {
                    var c = b.getElementById(a);
                    return c ? [
                        c
                    ] : [];
                }
            }) : (d.filter.ID = function(a) {
                var _$b = a.replace(_, aa);
                return function(a) {
                    var c = typeof a.getAttributeNode != 'undefined' && a.getAttributeNode('id');
                    return c && c.value === _$b;
                };
            }, d.find.ID = function(a, b) {
                if (typeof b.getElementById != 'undefined' && p) {
                    var c;
                    var d;
                    var e;
                    var f = b.getElementById(a);
                    if (f) {
                        if (c = f.getAttributeNode('id'), c && c.value === a) return [
                            f
                        ];
                        e = b.getElementsByName(a), d = 0;
                        while(f = e[d++])if (c = f.getAttributeNode('id'), c && c.value === a) return [
                            f
                        ];
                    }
                    return [];
                }
            }), d.find.TAG = c.getElementsByTagName ? function(a, b) {
                return typeof b.getElementsByTagName != 'undefined' ? b.getElementsByTagName(a) : c.qsa ? b.querySelectorAll(a) : void 0;
            } : function(a, b) {
                var c;
                var d = [];
                var e = 0;
                var f = b.getElementsByTagName(a);
                if (a === '*') {
                    while(c = f[e++])c.nodeType === 1 && d.push(c);
                    return d;
                }
                return f;
            }, d.find.CLASS = c.getElementsByClassName && function(a, b) {
                if (typeof b.getElementsByClassName != 'undefined' && p) return b.getElementsByClassName(a);
            }, r = [], q = [], (c.qsa = Y.test(n.querySelectorAll)) && (ja(function(a) {
                o.appendChild(a).innerHTML = "<a id='" + u + "'></a><select id='" + u + "-\r\\' msallowcapture=''><option selected=''></option></select>", a.querySelectorAll("[msallowcapture^='']").length && q.push('[*^$]=' + K + "*(?:''|\"\")"), a.querySelectorAll('[selected]').length || q.push('\\[' + K + '*(?:value|' + J + ')'), a.querySelectorAll('[id~=' + u + '-]').length || q.push('~='), a.querySelectorAll(':checked').length || q.push(':checked'), a.querySelectorAll('a#' + u + '+*').length || q.push('.#.+[+~]');
            }), ja(function(a) {
                a.innerHTML = "<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";
                var _$b = n.createElement('input');
                _$b.setAttribute('type', 'hidden'), a.appendChild(_$b).setAttribute('name', 'D'), a.querySelectorAll('[name=d]').length && q.push('name' + K + '*[*^$|!~]?='), a.querySelectorAll(':enabled').length !== 2 && q.push(':enabled', ':disabled'), o.appendChild(a).disabled = !0, a.querySelectorAll(':disabled').length !== 2 && q.push(':enabled', ':disabled'), a.querySelectorAll('*,:x'), q.push(',.*:');
            })), (c.matchesSelector = Y.test(s = o.matches || o.webkitMatchesSelector || o.mozMatchesSelector || o.oMatchesSelector || o.msMatchesSelector)) && ja(function(a) {
                c.disconnectedMatch = s.call(a, '*'), s.call(a, "[s!='']:x"), r.push('!=', N);
            }), q = q.length && new RegExp(q.join('|')), r = r.length && new RegExp(r.join('|')), _$b = Y.test(o.compareDocumentPosition), t = _$b || Y.test(o.contains) ? function t(a, b) {
                var c = a.nodeType === 9 ? a.documentElement : a;
                var d = b && b.parentNode;
                return a === d || !(!d || d.nodeType !== 1 || !(c.contains ? c.contains(d) : a.compareDocumentPosition && 16 & a.compareDocumentPosition(d)));
            } : function(a, b) {
                if (b) {
                    while(b = b.parentNode)if (b === a) return !0;
                }
                return !1;
            }, _$B = _$b ? function B(a, b) {
                if (a === b) return l = !0, 0;
                var d = !a.compareDocumentPosition - !b.compareDocumentPosition;
                return d || (d = (a.ownerDocument || a) === (b.ownerDocument || b) ? a.compareDocumentPosition(b) : 1, 1 & d || !c.sortDetached && b.compareDocumentPosition(a) === d ? a === n || a.ownerDocument === v && t(v, a) ? -1 : b === n || b.ownerDocument === v && t(v, b) ? 1 : k ? I(k, a) - I(k, b) : 0 : 4 & d ? -1 : 1);
            } : function(a, b) {
                if (a === b) return l = !0, 0;
                var c;
                var d = 0;
                var e = a.parentNode;
                var f = b.parentNode;
                var g = [
                    a
                ];
                var h = [
                    b
                ];
                if (!e || !f) return a === n ? -1 : b === n ? 1 : e ? -1 : f ? 1 : k ? I(k, a) - I(k, b) : 0;
                if (e === f) return la(a, b);
                c = a;
                while(c = c.parentNode)g.unshift(c);
                c = b;
                while(c = c.parentNode)h.unshift(c);
                while(g[d] === h[d])d++;
                return d ? la(g[d], h[d]) : g[d] === v ? -1 : h[d] === v ? 1 : 0;
            }, n) : n;
        }, ga.matches = function(a, b) {
            return ga(a, null, null, b);
        }, ga.matchesSelector = function(a, b) {
            if ((a.ownerDocument || a) !== n && m(a), b = b.replace(_$S, "='$1']"), c.matchesSelector && p && !A[b + ' '] && (!r || !r.test(b)) && (!q || !q.test(b))) try {
                var d = s.call(a, b);
                if (d || c.disconnectedMatch || a.document && a.document.nodeType !== 11) return d;
            } catch (e) {}
            return ga(b, n, null, [
                a
            ]).length > 0;
        }, ga.contains = function(a, b) {
            return (a.ownerDocument || a) !== n && m(a), t(a, b);
        }, ga.attr = function(a, b) {
            (a.ownerDocument || a) !== n && m(a);
            var e = d.attrHandle[b.toLowerCase()];
            var f = e && C.call(d.attrHandle, b.toLowerCase()) ? e(a, b, !p) : void 0;
            return void 0 !== f ? f : c.attributes || !p ? a.getAttribute(b) : (f = a.getAttributeNode(b)) && f.specified ? f.value : null;
        }, ga.escape = function(a) {
            return (a + '').replace(ba, ca);
        }, ga.error = function(a) {
            throw new Error('Syntax error, unrecognized expression: ' + a);
        }, ga.uniqueSort = function(a) {
            var _$b;
            var d = [];
            var e = 0;
            var f = 0;
            if (l = !c.detectDuplicates, k = !c.sortStable && a.slice(0), a.sort(_$B), l) {
                while(_$b = a[f++])_$b === a[f] && (e = d.push(f));
                while(e--)a.splice(d[e], 1);
            }
            return k = null, a;
        }, e = ga.getText = function(a) {
            var _$b;
            var c = '';
            var d = 0;
            var f = a.nodeType;
            if (f) {
                if (f === 1 || f === 9 || f === 11) {
                    if (typeof a.textContent == 'string') return a.textContent;
                    for(a = a.firstChild; a; a = a.nextSibling)c += e(a);
                } else if (f === 3 || f === 4) return a.nodeValue;
            } else while(_$b = a[d++])c += e(_$b);
            return c;
        }, d = ga.selectors = {
            cacheLength: 50,
            createPseudo: ia,
            match: _$V,
            attrHandle: {},
            find: {},
            relative: {
                '>': {
                    dir: 'parentNode',
                    first: !0
                },
                ' ': {
                    dir: 'parentNode'
                },
                '+': {
                    dir: 'previousSibling',
                    first: !0
                },
                '~': {
                    dir: 'previousSibling'
                }
            },
            preFilter: {
                ATTR: function ATTR(a) {
                    return a[1] = a[1].replace(_, aa), a[3] = (a[3] || a[4] || a[5] || '').replace(_, aa), a[2] === '~=' && (a[3] = ' ' + a[3] + ' '), a.slice(0, 4);
                },
                CHILD: function CHILD(a) {
                    return a[1] = a[1].toLowerCase(), a[1].slice(0, 3) === 'nth' ? (a[3] || ga.error(a[0]), a[4] = +(a[4] ? a[5] + (a[6] || 1) : 2 * (a[3] === 'even' || a[3] === 'odd')), a[5] = +(a[7] + a[8] || a[3] === 'odd')) : a[3] && ga.error(a[0]), a;
                },
                PSEUDO: function PSEUDO(a) {
                    var _$b;
                    var c = !a[6] && a[2];
                    return _$V.CHILD.test(a[0]) ? null : (a[3] ? a[2] = a[4] || a[5] || '' : c && T.test(c) && (_$b = g(c, !0)) && (_$b = c.indexOf(')', c.length - _$b) - c.length) && (a[0] = a[0].slice(0, _$b), a[2] = c.slice(0, _$b)), a.slice(0, 3));
                }
            },
            filter: {
                TAG: function TAG(a) {
                    var _$b = a.replace(_, aa).toLowerCase();
                    return a === '*' ? function() {
                        return !0;
                    } : function(a) {
                        return a.nodeName && a.nodeName.toLowerCase() === _$b;
                    };
                },
                CLASS: function CLASS(a) {
                    var _$b = y[a + ' '];
                    return _$b || (_$b = new RegExp('(^|' + K + ')' + a + '(' + K + '|$)')) && y(a, function(a) {
                        return _$b.test(typeof a.className == 'string' && a.className || typeof a.getAttribute != 'undefined' && a.getAttribute('class') || '');
                    });
                },
                ATTR: function ATTR(a, b, c) {
                    return function(d) {
                        var e = ga.attr(d, a);
                        return e == null ? b === '!=' : !b || (e += '', b === '=' ? e === c : b === '!=' ? e !== c : b === '^=' ? c && e.indexOf(c) === 0 : b === '*=' ? c && e.indexOf(c) > -1 : b === '$=' ? c && e.slice(-c.length) === c : b === '~=' ? (' ' + e.replace(O, ' ') + ' ').indexOf(c) > -1 : b === '|=' && (e === c || e.slice(0, c.length + 1) === c + '-'));
                    };
                },
                CHILD: function CHILD(a, b, c, d, e) {
                    var f = a.slice(0, 3) !== 'nth';
                    var g = a.slice(-4) !== 'last';
                    var h = b === 'of-type';
                    return d === 1 && e === 0 ? function(a) {
                        return !!a.parentNode;
                    } : function(b, c, i) {
                        var j;
                        var k;
                        var l;
                        var m;
                        var n;
                        var o;
                        var p = f !== g ? 'nextSibling' : 'previousSibling';
                        var q = b.parentNode;
                        var r = h && b.nodeName.toLowerCase();
                        var s = !i && !h;
                        var t = !1;
                        if (q) {
                            if (f) {
                                while(p){
                                    m = b;
                                    while(m = m[p])if (h ? m.nodeName.toLowerCase() === r : m.nodeType === 1) return !1;
                                    o = p = a === 'only' && !o && 'nextSibling';
                                }
                                return !0;
                            }
                            if (o = [
                                g ? q.firstChild : q.lastChild
                            ], g && s) {
                                m = q, l = m[u] || (m[u] = {}), k = l[m.uniqueID] || (l[m.uniqueID] = {}), j = k[a] || [], n = j[0] === w && j[1], t = n && j[2], m = n && q.childNodes[n];
                                while(m = ++n && m && m[p] || (t = n = 0) || o.pop())if (m.nodeType === 1 && ++t && m === b) {
                                    k[a] = [
                                        w,
                                        n,
                                        t
                                    ];
                                    break;
                                }
                            } else if (s && (m = b, l = m[u] || (m[u] = {}), k = l[m.uniqueID] || (l[m.uniqueID] = {}), j = k[a] || [], n = j[0] === w && j[1], t = n), t === !1) {
                                while(m = ++n && m && m[p] || (t = n = 0) || o.pop())if ((h ? m.nodeName.toLowerCase() === r : m.nodeType === 1) && ++t && (s && (l = m[u] || (m[u] = {}), k = l[m.uniqueID] || (l[m.uniqueID] = {}), k[a] = [
                                    w,
                                    t
                                ]), m === b)) break;
                            }
                            return t -= e, t === d || t % d === 0 && t / d >= 0;
                        }
                    };
                },
                PSEUDO: function PSEUDO(a, b) {
                    var c;
                    var e = d.pseudos[a] || d.setFilters[a.toLowerCase()] || ga.error('unsupported pseudo: ' + a);
                    return e[u] ? e(b) : e.length > 1 ? (c = [
                        a,
                        a,
                        '',
                        b
                    ], d.setFilters.hasOwnProperty(a.toLowerCase()) ? ia(function(a, c) {
                        var d;
                        var f = e(a, b);
                        var g = f.length;
                        while(g--)d = I(a, f[g]), a[d] = !(c[d] = f[g]);
                    }) : function(a) {
                        return e(a, 0, c);
                    }) : e;
                }
            },
            pseudos: {
                not: ia(function(a) {
                    var _$b = [];
                    var c = [];
                    var d = h(a.replace(P, '$1'));
                    return d[u] ? ia(function(a, b, c, e) {
                        var f;
                        var g = d(a, null, e, []);
                        var h = a.length;
                        while(h--)(f = g[h]) && (a[h] = !(b[h] = f));
                    }) : function(a, e, f) {
                        return _$b[0] = a, d(_$b, null, f, c), _$b[0] = null, !c.pop();
                    };
                }),
                has: ia(function(a) {
                    return function(b) {
                        return ga(a, b).length > 0;
                    };
                }),
                contains: ia(function(a) {
                    return a = a.replace(_, aa), function(b) {
                        return (b.textContent || b.innerText || e(b)).indexOf(a) > -1;
                    };
                }),
                lang: ia(function(a) {
                    return U.test(a || '') || ga.error('unsupported lang: ' + a), a = a.replace(_, aa).toLowerCase(), function(b) {
                        var c;
                        do if (c = p ? b.lang : b.getAttribute('xml:lang') || b.getAttribute('lang')) return c = c.toLowerCase(), c === a || c.indexOf(a + '-') === 0;
                        while ((b = b.parentNode) && b.nodeType === 1);
                        return !1;
                    };
                }),
                target: function target(b) {
                    var c = a.location && a.location.hash;
                    return c && c.slice(1) === b.id;
                },
                root: function root(a) {
                    return a === o;
                },
                focus: function focus(a) {
                    return a === n.activeElement && (!n.hasFocus || n.hasFocus()) && !!(a.type || a.href || ~a.tabIndex);
                },
                enabled: oa(!1),
                disabled: oa(!0),
                checked: function checked(a) {
                    var _$b = a.nodeName.toLowerCase();
                    return _$b === 'input' && !!a.checked || _$b === 'option' && !!a.selected;
                },
                selected: function selected(a) {
                    return a.parentNode && a.parentNode.selectedIndex, a.selected === !0;
                },
                empty: function empty(a) {
                    for(a = a.firstChild; a; a = a.nextSibling)if (a.nodeType < 6) return !1;
                    return !0;
                },
                parent: function parent(a) {
                    return !d.pseudos.empty(a);
                },
                header: function header(a) {
                    return X.test(a.nodeName);
                },
                input: function input(a) {
                    return W.test(a.nodeName);
                },
                button: function button(a) {
                    var _$b = a.nodeName.toLowerCase();
                    return _$b === 'input' && a.type === 'button' || _$b === 'button';
                },
                text: function text(a) {
                    var _$b;
                    return a.nodeName.toLowerCase() === 'input' && a.type === 'text' && ((_$b = a.getAttribute('type')) == null || _$b.toLowerCase() === 'text');
                },
                first: pa(function() {
                    return [
                        0
                    ];
                }),
                last: pa(function(a, b) {
                    return [
                        b - 1
                    ];
                }),
                eq: pa(function(a, b, c) {
                    return [
                        c < 0 ? c + b : c
                    ];
                }),
                even: pa(function(a, b) {
                    for(var c = 0; c < b; c += 2)a.push(c);
                    return a;
                }),
                odd: pa(function(a, b) {
                    for(var c = 1; c < b; c += 2)a.push(c);
                    return a;
                }),
                lt: pa(function(a, b, c) {
                    for(var d = c < 0 ? c + b : c; --d >= 0;)a.push(d);
                    return a;
                }),
                gt: pa(function(a, b, c) {
                    for(var d = c < 0 ? c + b : c; ++d < b;)a.push(d);
                    return a;
                })
            }
        }, d.pseudos.nth = d.pseudos.eq;
        for(_$b in {
            radio: !0,
            checkbox: !0,
            file: !0,
            password: !0,
            image: !0
        })d.pseudos[_$b] = ma(_$b);
        for(_$b in {
            submit: !0,
            reset: !0
        })d.pseudos[_$b] = na(_$b);
        function ra() {}
        ra.prototype = d.filters = d.pseudos, d.setFilters = new ra(), g = ga.tokenize = function g(a, b) {
            var c;
            var e;
            var f;
            var g;
            var h;
            var i;
            var j;
            var k = z[a + ' '];
            if (k) return b ? 0 : k.slice(0);
            h = a, i = [], j = d.preFilter;
            while(h){
                c && !(e = Q.exec(h)) || (e && (h = h.slice(e[0].length) || h), i.push(f = [])), c = !1, (e = R.exec(h)) && (c = e.shift(), f.push({
                    value: c,
                    type: e[0].replace(P, ' ')
                }), h = h.slice(c.length));
                for(g in d.filter)!(e = _$V[g].exec(h)) || j[g] && !(e = j[g](e)) || (c = e.shift(), f.push({
                    value: c,
                    type: g,
                    matches: e
                }), h = h.slice(c.length));
                if (!c) break;
            }
            return b ? h.length : h ? ga.error(a) : z(a, i).slice(0);
        };
        function sa(a) {
            for(var _$b = 0, c = a.length, d = ''; _$b < c; _$b++)d += a[_$b].value;
            return d;
        }
        function ta(a, b, c) {
            var d = b.dir;
            var e = b.next;
            var f = e || d;
            var g = c && f === 'parentNode';
            var h = x++;
            return b.first ? function(b, c, e) {
                while(b = b[d])if (b.nodeType === 1 || g) return a(b, c, e);
                return !1;
            } : function(b, c, i) {
                var j;
                var k;
                var l;
                var m = [
                    w,
                    h
                ];
                if (i) {
                    while(b = b[d])if ((b.nodeType === 1 || g) && a(b, c, i)) return !0;
                } else while(b = b[d])if (b.nodeType === 1 || g) if (l = b[u] || (b[u] = {}), k = l[b.uniqueID] || (l[b.uniqueID] = {}), e && e === b.nodeName.toLowerCase()) b = b[d] || b;
                else {
                    if ((j = k[f]) && j[0] === w && j[1] === h) return m[2] = j[2];
                    if (k[f] = m, m[2] = a(b, c, i)) return !0;
                }
                return !1;
            };
        }
        function ua(a) {
            return a.length > 1 ? function(b, c, d) {
                var e = a.length;
                while(e--)if (!a[e](b, c, d)) return !1;
                return !0;
            } : a[0];
        }
        function va(a, b, c) {
            for(var d = 0, e = b.length; d < e; d++)ga(a, b[d], c);
            return c;
        }
        function wa(a, b, c, d, e) {
            for(var f, g = [], h = 0, i = a.length, j = b != null; h < i; h++)(f = a[h]) && (c && !c(f, d, e) || (g.push(f), j && b.push(h)));
            return g;
        }
        function xa(a, b, c, d, e, f) {
            return d && !d[u] && (d = xa(d)), e && !e[u] && (e = xa(e, f)), ia(function(f, g, h, i) {
                var j;
                var k;
                var l;
                var m = [];
                var n = [];
                var o = g.length;
                var p = f || va(b || '*', h.nodeType ? [
                    h
                ] : h, []);
                var q = !a || !f && b ? p : wa(p, m, a, h, i);
                var r = c ? e || (f ? a : o || d) ? [] : g : q;
                if (c && c(q, r, h, i), d) {
                    j = wa(r, n), d(j, [], h, i), k = j.length;
                    while(k--)(l = j[k]) && (r[n[k]] = !(q[n[k]] = l));
                }
                if (f) {
                    if (e || a) {
                        if (e) {
                            j = [], k = r.length;
                            while(k--)(l = r[k]) && j.push(q[k] = l);
                            e(null, r = [], j, i);
                        }
                        k = r.length;
                        while(k--)(l = r[k]) && (j = e ? I(f, l) : m[k]) > -1 && (f[j] = !(g[j] = l));
                    }
                } else r = wa(r === g ? r.splice(o, r.length) : r), e ? e(null, g, r, i) : G.apply(g, r);
            });
        }
        function ya(a) {
            for(var _$b, c, e, f = a.length, g = d.relative[a[0].type], h = g || d.relative[' '], i = g ? 1 : 0, k = ta(function(a) {
                return a === _$b;
            }, h, !0), l = ta(function(a) {
                return I(_$b, a) > -1;
            }, h, !0), m = [
                function(a, c, d) {
                    var e = !g && (d || c !== j) || ((_$b = c).nodeType ? k(a, c, d) : l(a, c, d));
                    return _$b = null, e;
                }
            ]; i < f; i++)if (c = d.relative[a[i].type]) m = [
                ta(ua(m), c)
            ];
            else {
                if (c = d.filter[a[i].type].apply(null, a[i].matches), c[u]) {
                    for(e = ++i; e < f; e++)if (d.relative[a[e].type]) break;
                    return xa(i > 1 && ua(m), i > 1 && sa(a.slice(0, i - 1).concat({
                        value: a[i - 2].type === ' ' ? '*' : ''
                    })).replace(P, '$1'), c, i < e && ya(a.slice(i, e)), e < f && ya(a = a.slice(e)), e < f && sa(a));
                }
                m.push(c);
            }
            return ua(m);
        }
        function za(a, b) {
            var c = b.length > 0;
            var e = a.length > 0;
            var f = function f(f, g, h, i, k) {
                var l;
                var o;
                var q;
                var r = 0;
                var s = '0';
                var t = f && [];
                var u = [];
                var v = j;
                var x = f || e && d.find.TAG('*', k);
                var y = w += v == null ? 1 : Math.random() || 0.1;
                var z = x.length;
                for(k && (j = g === n || g || k); s !== z && (l = x[s]) != null; s++){
                    if (e && l) {
                        o = 0, g || l.ownerDocument === n || (m(l), h = !p);
                        while(q = a[o++])if (q(l, g || n, h)) {
                            i.push(l);
                            break;
                        }
                        k && (w = y);
                    }
                    c && ((l = !q && l) && r--, f && t.push(l));
                }
                if (r += s, c && s !== r) {
                    o = 0;
                    while(q = b[o++])q(t, u, g, h);
                    if (f) {
                        if (r > 0) while(s--)t[s] || u[s] || (u[s] = E.call(i));
                        u = wa(u);
                    }
                    G.apply(i, u), k && !f && u.length > 0 && r + b.length > 1 && ga.uniqueSort(i);
                }
                return k && (w = y, j = v), t;
            };
            return c ? ia(f) : f;
        }
        return h = ga.compile = function h(a, b) {
            var c;
            var d = [];
            var e = [];
            var f = A[a + ' '];
            if (!f) {
                b || (b = g(a)), c = b.length;
                while(c--)f = ya(b[c]), f[u] ? d.push(f) : e.push(f);
                f = A(a, za(e, d)), f.selector = a;
            }
            return f;
        }, i = ga.select = function i(a, b, c, e) {
            var f;
            var i;
            var j;
            var k;
            var l;
            var m = typeof a == 'function' && a;
            var n = !e && g(a = m.selector || a);
            if (c = c || [], n.length === 1) {
                if (i = n[0] = n[0].slice(0), i.length > 2 && (j = i[0]).type === 'ID' && b.nodeType === 9 && p && d.relative[i[1].type]) {
                    if (b = (d.find.ID(j.matches[0].replace(_, aa), b) || [])[0], !b) return c;
                    m && (b = b.parentNode), a = a.slice(i.shift().value.length);
                }
                f = _$V.needsContext.test(a) ? 0 : i.length;
                while(f--){
                    if (j = i[f], d.relative[k = j.type]) break;
                    if ((l = d.find[k]) && (e = l(j.matches[0].replace(_, aa), $.test(i[0].type) && qa(b.parentNode) || b))) {
                        if (i.splice(f, 1), a = e.length && sa(i), !a) return G.apply(c, e), c;
                        break;
                    }
                }
            }
            return (m || h(a, n))(e, b, !p, c, !b || $.test(a) && qa(b.parentNode) || b), c;
        }, c.sortStable = u.split('').sort(_$B).join('') === u, c.detectDuplicates = !!l, m(), c.sortDetached = ja(function(a) {
            return 1 & a.compareDocumentPosition(n.createElement('fieldset'));
        }), ja(function(a) {
            return a.innerHTML = "<a href='#'></a>", a.firstChild.getAttribute('href') === '#';
        }) || ka('type|href|height|width', function(a, b, c) {
            if (!c) return a.getAttribute(b, b.toLowerCase() === 'type' ? 1 : 2);
        }), c.attributes && ja(function(a) {
            return a.innerHTML = '<input/>', a.firstChild.setAttribute('value', ''), a.firstChild.getAttribute('value') === '';
        }) || ka('value', function(a, b, c) {
            if (!c && a.nodeName.toLowerCase() === 'input') return a.defaultValue;
        }), ja(function(a) {
            return a.getAttribute('disabled') == null;
        }) || ka(J, function(a, b, c) {
            var d;
            if (!c) return a[b] === !0 ? b.toLowerCase() : (d = a.getAttributeNode(b)) && d.specified ? d.value : null;
        }), ga;
    }(a);
    r.find = x, r.expr = x.selectors, r.expr[':'] = r.expr.pseudos, r.uniqueSort = r.unique = x.uniqueSort, r.text = x.getText, r.isXMLDoc = x.isXML, r.contains = x.contains, r.escapeSelector = x.escape;
    var y = function y(a, b, c) {
        var d = [];
        var e = void 0 !== c;
        while((a = a[b]) && a.nodeType !== 9)if (a.nodeType === 1) {
            if (e && r(a).is(c)) break;
            d.push(a);
        }
        return d;
    };
    var z = function z(a, b) {
        for(var c = []; a; a = a.nextSibling)a.nodeType === 1 && a !== b && c.push(a);
        return c;
    };
    var A = r.expr.match.needsContext;
    function B(a, b) {
        return a.nodeName && a.nodeName.toLowerCase() === b.toLowerCase();
    }
    var C = /^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;
    var D = /^.[^:#\[\.,]*$/;
    function E(a, b, c) {
        return r.isFunction(b) ? r.grep(a, function(a, d) {
            return !!b.call(a, d, a) !== c;
        }) : b.nodeType ? r.grep(a, function(a) {
            return a === b !== c;
        }) : typeof b != 'string' ? r.grep(a, function(a) {
            return i.call(b, a) > -1 !== c;
        }) : D.test(b) ? r.filter(b, a, c) : (b = r.filter(b, a), r.grep(a, function(a) {
            return i.call(b, a) > -1 !== c && a.nodeType === 1;
        }));
    }
    r.filter = function(a, b, c) {
        var d = b[0];
        return c && (a = ':not(' + a + ')'), b.length === 1 && d.nodeType === 1 ? r.find.matchesSelector(d, a) ? [
            d
        ] : [] : r.find.matches(a, r.grep(b, function(a) {
            return a.nodeType === 1;
        }));
    }, r.fn.extend({
        find: function find(a) {
            var _$b;
            var c;
            var d = this.length;
            var e = this;
            if (typeof a != 'string') return this.pushStack(r(a).filter(function() {
                for(_$b = 0; _$b < d; _$b++)if (r.contains(e[_$b], this)) return !0;
            }));
            for(c = this.pushStack([]), _$b = 0; _$b < d; _$b++)r.find(a, e[_$b], c);
            return d > 1 ? r.uniqueSort(c) : c;
        },
        filter: function filter(a) {
            return this.pushStack(E(this, a || [], !1));
        },
        not: function not(a) {
            return this.pushStack(E(this, a || [], !0));
        },
        is: function is(a) {
            return !!E(this, typeof a == 'string' && A.test(a) ? r(a) : a || [], !1).length;
        }
    });
    var F;
    var G = /^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;
    var H = r.fn.init = function H(a, b, c) {
        var e, f;
        if (!a) return this;
        if (c = c || F, typeof a == 'string') {
            if (e = a[0] === '<' && a[a.length - 1] === '>' && a.length >= 3 ? [
                null,
                a,
                null
            ] : G.exec(a), !e || !e[1] && b) return !b || b.jquery ? (b || c).find(a) : this.constructor(b).find(a);
            if (e[1]) {
                if (b = _instanceof(b, r) ? b[0] : b, r.merge(this, r.parseHTML(e[1], b && b.nodeType ? b.ownerDocument || b : d, !0)), C.test(e[1]) && r.isPlainObject(b)) for(e in b)r.isFunction(this[e]) ? this[e](b[e]) : this.attr(e, b[e]);
                return this;
            }
            return f = d.getElementById(e[2]), f && (this[0] = f, this.length = 1), this;
        }
        return a.nodeType ? (this[0] = a, this.length = 1, this) : r.isFunction(a) ? void 0 !== c.ready ? c.ready(a) : a(r) : r.makeArray(a, this);
    };
    H.prototype = r.fn, F = r(d);
    var I = /^(?:parents|prev(?:Until|All))/;
    var J = {
        children: !0,
        contents: !0,
        next: !0,
        prev: !0
    };
    r.fn.extend({
        has: function has(a) {
            var _$b = r(a, this);
            var c = _$b.length;
            return this.filter(function() {
                for(var _$a = 0; _$a < c; _$a++)if (r.contains(this, _$b[_$a])) return !0;
            });
        },
        closest: function closest(a, b) {
            var c;
            var d = 0;
            var e = this.length;
            var f = [];
            var g = typeof a != 'string' && r(a);
            if (!A.test(a)) {
                for(; d < e; d++)for(c = this[d]; c && c !== b; c = c.parentNode)if (c.nodeType < 11 && (g ? g.index(c) > -1 : c.nodeType === 1 && r.find.matchesSelector(c, a))) {
                    f.push(c);
                    break;
                }
            }
            return this.pushStack(f.length > 1 ? r.uniqueSort(f) : f);
        },
        index: function index(a) {
            return a ? typeof a == 'string' ? i.call(r(a), this[0]) : i.call(this, a.jquery ? a[0] : a) : this[0] && this[0].parentNode ? this.first().prevAll().length : -1;
        },
        add: function add(a, b) {
            return this.pushStack(r.uniqueSort(r.merge(this.get(), r(a, b))));
        },
        addBack: function addBack(a) {
            return this.add(a == null ? this.prevObject : this.prevObject.filter(a));
        }
    });
    function K(a, b) {
        while((a = a[b]) && a.nodeType !== 1);
        return a;
    }
    r.each({
        parent: function parent(a) {
            var _$b = a.parentNode;
            return _$b && _$b.nodeType !== 11 ? _$b : null;
        },
        parents: function parents(a) {
            return y(a, 'parentNode');
        },
        parentsUntil: function parentsUntil(a, b, c) {
            return y(a, 'parentNode', c);
        },
        next: function next(a) {
            return K(a, 'nextSibling');
        },
        prev: function prev(a) {
            return K(a, 'previousSibling');
        },
        nextAll: function nextAll(a) {
            return y(a, 'nextSibling');
        },
        prevAll: function prevAll(a) {
            return y(a, 'previousSibling');
        },
        nextUntil: function nextUntil(a, b, c) {
            return y(a, 'nextSibling', c);
        },
        prevUntil: function prevUntil(a, b, c) {
            return y(a, 'previousSibling', c);
        },
        siblings: function siblings(a) {
            return z((a.parentNode || {}).firstChild, a);
        },
        children: function children(a) {
            return z(a.firstChild);
        },
        contents: function contents(a) {
            return B(a, 'iframe') ? a.contentDocument : (B(a, 'template') && (a = a.content || a), r.merge([], a.childNodes));
        }
    }, function(a, b) {
        r.fn[a] = function(c, d) {
            var e = r.map(this, b, c);
            return a.slice(-5) !== 'Until' && (d = c), d && typeof d == 'string' && (e = r.filter(d, e)), this.length > 1 && (J[a] || r.uniqueSort(e), I.test(a) && e.reverse()), this.pushStack(e);
        };
    });
    var L = /[^\x20\t\r\n\f]+/g;
    function M(a) {
        var _$b = {};
        return r.each(a.match(L) || [], function(a, c) {
            _$b[c] = !0;
        }), _$b;
    }
    r.Callbacks = function(a) {
        a = typeof a == 'string' ? M(a) : r.extend({}, a);
        var _$b;
        var c;
        var d;
        var e;
        var f = [];
        var g = [];
        var h = -1;
        var i = function i() {
            for(e = e || a.once, d = _$b = !0; g.length; h = -1){
                c = g.shift();
                while(++h < f.length)f[h].apply(c[0], c[1]) === !1 && a.stopOnFalse && (h = f.length, c = !1);
            }
            a.memory || (c = !1), _$b = !1, e && (f = c ? [] : '');
        };
        var j = {
            add: function add() {
                return f && (c && !_$b && (h = f.length - 1, g.push(c)), function d(b) {
                    r.each(b, function(b, c) {
                        r.isFunction(c) ? a.unique && j.has(c) || f.push(c) : c && c.length && r.type(c) !== 'string' && d(c);
                    });
                }(arguments), c && !_$b && i()), this;
            },
            remove: function remove() {
                return r.each(arguments, function(a, b) {
                    var c;
                    while((c = r.inArray(b, f, c)) > -1)f.splice(c, 1), c <= h && h--;
                }), this;
            },
            has: function has(a) {
                return a ? r.inArray(a, f) > -1 : f.length > 0;
            },
            empty: function empty() {
                return f && (f = []), this;
            },
            disable: function disable() {
                return e = g = [], f = c = '', this;
            },
            disabled: function disabled() {
                return !f;
            },
            lock: function lock() {
                return e = g = [], c || _$b || (f = c = ''), this;
            },
            locked: function locked() {
                return !!e;
            },
            fireWith: function fireWith(a, c) {
                return e || (c = c || [], c = [
                    a,
                    c.slice ? c.slice() : c
                ], g.push(c), _$b || i()), this;
            },
            fire: function fire() {
                return j.fireWith(this, arguments), this;
            },
            fired: function fired() {
                return !!d;
            }
        };
        return j;
    };
    function N(a) {
        return a;
    }
    function O(a) {
        throw a;
    }
    function P(a, b, c, d) {
        var e;
        try {
            a && r.isFunction(e = a.promise) ? e.call(a).done(b).fail(c) : a && r.isFunction(e = a.then) ? e.call(a, b, c) : b.apply(void 0, [
                a
            ].slice(d));
        } catch (a) {
            c.apply(void 0, [
                a
            ]);
        }
    }
    r.extend({
        Deferred: function Deferred(b) {
            var c = [
                [
                    'notify',
                    'progress',
                    r.Callbacks('memory'),
                    r.Callbacks('memory'),
                    2
                ],
                [
                    'resolve',
                    'done',
                    r.Callbacks('once memory'),
                    r.Callbacks('once memory'),
                    0,
                    'resolved'
                ],
                [
                    'reject',
                    'fail',
                    r.Callbacks('once memory'),
                    r.Callbacks('once memory'),
                    1,
                    'rejected'
                ]
            ];
            var d = 'pending';
            var e = {
                state: function state() {
                    return d;
                },
                always: function always() {
                    return f.done(arguments).fail(arguments), this;
                },
                catch: function _catch(a) {
                    return e.then(null, a);
                },
                pipe: function pipe() {
                    var _$a = arguments;
                    return r.Deferred(function(b) {
                        r.each(c, function(c, d) {
                            var e = r.isFunction(_$a[d[4]]) && _$a[d[4]];
                            f[d[1]](function() {
                                var _$a = e && e.apply(this, arguments);
                                _$a && r.isFunction(_$a.promise) ? _$a.promise().progress(b.notify).done(b.resolve).fail(b.reject) : b[d[0] + 'With'](this, e ? [
                                    _$a
                                ] : arguments);
                            });
                        }), _$a = null;
                    }).promise();
                },
                then: function then(b, d, e) {
                    var f = 0;
                    function g(b, c, d, e) {
                        return function() {
                            var h = this;
                            var i = arguments;
                            var j = function j() {
                                var _$a, j;
                                if (!(b < f)) {
                                    if (_$a = d.apply(h, i), _$a === c.promise()) throw new TypeError('Thenable self-resolution');
                                    j = _$a && ((typeof _$a === "undefined" ? "undefined" : _type_of(_$a)) == 'object' || typeof _$a == 'function') && _$a.then, r.isFunction(j) ? e ? j.call(_$a, g(f, c, N, e), g(f, c, O, e)) : (f++, j.call(_$a, g(f, c, N, e), g(f, c, O, e), g(f, c, N, c.notifyWith))) : (d !== N && (h = void 0, i = [
                                        _$a
                                    ]), (e || c.resolveWith)(h, i));
                                }
                            };
                            var k = e ? j : function k1() {
                                try {
                                    j();
                                } catch (a) {
                                    r.Deferred.exceptionHook && r.Deferred.exceptionHook(a, k.stackTrace), b + 1 >= f && (d !== O && (h = void 0, i = [
                                        a
                                    ]), c.rejectWith(h, i));
                                }
                            };
                            b ? k() : (r.Deferred.getStackHook && (k.stackTrace = r.Deferred.getStackHook()), a.setTimeout(k));
                        };
                    }
                    return r.Deferred(function(a) {
                        c[0][3].add(g(0, a, r.isFunction(e) ? e : N, a.notifyWith)), c[1][3].add(g(0, a, r.isFunction(b) ? b : N)), c[2][3].add(g(0, a, r.isFunction(d) ? d : O));
                    }).promise();
                },
                promise: function promise(a) {
                    return a != null ? r.extend(a, e) : e;
                }
            };
            var f = {};
            return r.each(c, function(a, b) {
                var g = b[2];
                var h = b[5];
                e[b[1]] = g.add, h && g.add(function() {
                    d = h;
                }, c[3 - a][2].disable, c[0][2].lock), g.add(b[3].fire), f[b[0]] = function() {
                    return f[b[0] + 'With'](this === f ? void 0 : this, arguments), this;
                }, f[b[0] + 'With'] = g.fireWith;
            }), e.promise(f), b && b.call(f, f), f;
        },
        when: function when(a) {
            var _$b = arguments.length;
            var c = _$b;
            var d = Array(c);
            var e = f.call(arguments);
            var g = r.Deferred();
            var h = function h(a) {
                return function(c) {
                    d[a] = this, e[a] = arguments.length > 1 ? f.call(arguments) : c, --_$b || g.resolveWith(d, e);
                };
            };
            if (_$b <= 1 && (P(a, g.done(h(c)).resolve, g.reject, !_$b), g.state() === 'pending' || r.isFunction(e[c] && e[c].then))) return g.then();
            while(c--)P(e[c], h(c), g.reject);
            return g.promise();
        }
    });
    var Q = /^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;
    r.Deferred.exceptionHook = function(b, c) {
        a.console && a.console.warn && b && Q.test(b.name) && a.console.warn('jQuery.Deferred exception: ' + b.message, b.stack, c);
    }, r.readyException = function(b) {
        a.setTimeout(function() {
            throw b;
        });
    };
    var R = r.Deferred();
    r.fn.ready = function(a) {
        return R.then(a).catch(function(a) {
            r.readyException(a);
        }), this;
    }, r.extend({
        isReady: !1,
        readyWait: 1,
        ready: function ready(a) {
            (a === !0 ? --r.readyWait : r.isReady) || (r.isReady = !0, a !== !0 && --r.readyWait > 0 || R.resolveWith(d, [
                r
            ]));
        }
    }), r.ready.then = R.then;
    function S() {
        d.removeEventListener('DOMContentLoaded', S), a.removeEventListener('load', S), r.ready();
    }
    d.readyState === 'complete' || d.readyState !== 'loading' && !d.documentElement.doScroll ? a.setTimeout(r.ready) : (d.addEventListener('DOMContentLoaded', S), a.addEventListener('load', S));
    var T = function T1(a, b, c, d, e, f, g) {
        var h = 0;
        var i = a.length;
        var j = c == null;
        if (r.type(c) === 'object') {
            e = !0;
            for(h in c)T(a, b, h, c[h], !0, f, g);
        } else if (void 0 !== d && (e = !0, r.isFunction(d) || (g = !0), j && (g ? (b.call(a, d), b = null) : (j = b, b = function b(a, b, c) {
            return j.call(r(a), c);
        })), b)) for(; h < i; h++)b(a[h], c, g ? d : d.call(a[h], h, b(a[h], c)));
        return e ? a : j ? b.call(a) : i ? b(a[0], c) : f;
    };
    var U = function U(a) {
        return a.nodeType === 1 || a.nodeType === 9 || !+a.nodeType;
    };
    function V() {
        this.expando = r.expando + V.uid++;
    }
    V.uid = 1, V.prototype = {
        cache: function cache(a) {
            var _$b = a[this.expando];
            return _$b || (_$b = {}, U(a) && (a.nodeType ? a[this.expando] = _$b : Object.defineProperty(a, this.expando, {
                value: _$b,
                configurable: !0
            }))), _$b;
        },
        set: function set(a, b, c) {
            var d;
            var e = this.cache(a);
            if (typeof b == 'string') e[r.camelCase(b)] = c;
            else for(d in b)e[r.camelCase(d)] = b[d];
            return e;
        },
        get: function get(a, b) {
            return void 0 === b ? this.cache(a) : a[this.expando] && a[this.expando][r.camelCase(b)];
        },
        access: function access(a, b, c) {
            return void 0 === b || b && typeof b == 'string' && void 0 === c ? this.get(a, b) : (this.set(a, b, c), void 0 !== c ? c : b);
        },
        remove: function remove(a, b) {
            var c;
            var d = a[this.expando];
            if (void 0 !== d) {
                if (void 0 !== b) {
                    Array.isArray(b) ? b = b.map(r.camelCase) : (b = r.camelCase(b), b = b in d ? [
                        b
                    ] : b.match(L) || []), c = b.length;
                    while(c--)delete d[b[c]];
                }
                (void 0 === b || r.isEmptyObject(d)) && (a.nodeType ? a[this.expando] = void 0 : delete a[this.expando]);
            }
        },
        hasData: function hasData(a) {
            var _$b = a[this.expando];
            return void 0 !== _$b && !r.isEmptyObject(_$b);
        }
    };
    var W = new V();
    var X = new V();
    var Y = /^(?:\{[\w\W]*\}|\[[\w\W]*\])$/;
    var Z = /[A-Z]/g;
    function $(a) {
        return a === 'true' || a !== 'false' && (a === 'null' ? null : a === +a + '' ? +a : Y.test(a) ? JSON.parse(a) : a);
    }
    function _(a, b, c) {
        var d;
        if (void 0 === c && a.nodeType === 1) if (d = 'data-' + b.replace(Z, '-$&').toLowerCase(), c = a.getAttribute(d), typeof c == 'string') {
            try {
                c = $(c);
            } catch (e) {}
            X.set(a, b, c);
        } else c = void 0;
        return c;
    }
    r.extend({
        hasData: function hasData(a) {
            return X.hasData(a) || W.hasData(a);
        },
        data: function data(a, b, c) {
            return X.access(a, b, c);
        },
        removeData: function removeData(a, b) {
            X.remove(a, b);
        },
        _data: function _data(a, b, c) {
            return W.access(a, b, c);
        },
        _removeData: function _removeData(a, b) {
            W.remove(a, b);
        }
    }), r.fn.extend({
        data: function data(a, b) {
            var c;
            var d;
            var e;
            var f = this[0];
            var g = f && f.attributes;
            if (void 0 === a) {
                if (this.length && (e = X.get(f), f.nodeType === 1 && !W.get(f, 'hasDataAttrs'))) {
                    c = g.length;
                    while(c--)g[c] && (d = g[c].name, d.indexOf('data-') === 0 && (d = r.camelCase(d.slice(5)), _(f, d, e[d])));
                    W.set(f, 'hasDataAttrs', !0);
                }
                return e;
            }
            return (typeof a === "undefined" ? "undefined" : _type_of(a)) == 'object' ? this.each(function() {
                X.set(this, a);
            }) : T(this, function(b) {
                var c;
                if (f && void 0 === b) {
                    if (c = X.get(f, a), void 0 !== c) return c;
                    if (c = _(f, a), void 0 !== c) return c;
                } else this.each(function() {
                    X.set(this, a, b);
                });
            }, null, b, arguments.length > 1, null, !0);
        },
        removeData: function removeData(a) {
            return this.each(function() {
                X.remove(this, a);
            });
        }
    }), r.extend({
        queue: function queue(a, b, c) {
            var d;
            if (a) return b = (b || 'fx') + 'queue', d = W.get(a, b), c && (!d || Array.isArray(c) ? d = W.access(a, b, r.makeArray(c)) : d.push(c)), d || [];
        },
        dequeue: function dequeue(a, b) {
            b = b || 'fx';
            var c = r.queue(a, b);
            var d = c.length;
            var e = c.shift();
            var f = r._queueHooks(a, b);
            var g = function g() {
                r.dequeue(a, b);
            };
            e === 'inprogress' && (e = c.shift(), d--), e && (b === 'fx' && c.unshift('inprogress'), delete f.stop, e.call(a, g, f)), !d && f && f.empty.fire();
        },
        _queueHooks: function _queueHooks(a, b) {
            var c = b + 'queueHooks';
            return W.get(a, c) || W.access(a, c, {
                empty: r.Callbacks('once memory').add(function() {
                    W.remove(a, [
                        b + 'queue',
                        c
                    ]);
                })
            });
        }
    }), r.fn.extend({
        queue: function queue(a, b) {
            var c = 2;
            return typeof a != 'string' && (b = a, a = 'fx', c--), arguments.length < c ? r.queue(this[0], a) : void 0 === b ? this : this.each(function() {
                var c = r.queue(this, a, b);
                r._queueHooks(this, a), a === 'fx' && c[0] !== 'inprogress' && r.dequeue(this, a);
            });
        },
        dequeue: function dequeue(a) {
            return this.each(function() {
                r.dequeue(this, a);
            });
        },
        clearQueue: function clearQueue(a) {
            return this.queue(a || 'fx', []);
        },
        promise: function promise(a, b) {
            var c;
            var d = 1;
            var e = r.Deferred();
            var f = this;
            var g = this.length;
            var h = function h() {
                --d || e.resolveWith(f, [
                    f
                ]);
            };
            typeof a != 'string' && (b = a, a = void 0), a = a || 'fx';
            while(g--)c = W.get(f[g], a + 'queueHooks'), c && c.empty && (d++, c.empty.add(h));
            return h(), e.promise(b);
        }
    });
    var aa = /[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source;
    var ba = new RegExp('^(?:([+-])=|)(' + aa + ')([a-z%]*)$', 'i');
    var ca = [
        'Top',
        'Right',
        'Bottom',
        'Left'
    ];
    var da = function da(a, b) {
        return a = b || a, a.style.display === 'none' || a.style.display === '' && r.contains(a.ownerDocument, a) && r.css(a, 'display') === 'none';
    };
    var ea = function ea(a, b, c, d) {
        var e;
        var f;
        var g = {};
        for(f in b)g[f] = a.style[f], a.style[f] = b[f];
        e = c.apply(a, d || []);
        for(f in b)a.style[f] = g[f];
        return e;
    };
    function fa(a, b, c, d) {
        var e;
        var f = 1;
        var g = 20;
        var h = d ? function h() {
            return d.cur();
        } : function() {
            return r.css(a, b, '');
        };
        var i = h();
        var j = c && c[3] || (r.cssNumber[b] ? '' : 'px');
        var k = (r.cssNumber[b] || j !== 'px' && +i) && ba.exec(r.css(a, b));
        if (k && k[3] !== j) {
            j = j || k[3], c = c || [], k = +i || 1;
            do f = f || '.5', k /= f, r.style(a, b, k + j);
            while (f !== (f = h() / i) && f !== 1 && --g);
        }
        return c && (k = +k || +i || 0, e = c[1] ? k + (c[1] + 1) * c[2] : +c[2], d && (d.unit = j, d.start = k, d.end = e)), e;
    }
    var ga = {};
    function ha(a) {
        var _$b;
        var c = a.ownerDocument;
        var d = a.nodeName;
        var e = ga[d];
        return e || (_$b = c.body.appendChild(c.createElement(d)), e = r.css(_$b, 'display'), _$b.parentNode.removeChild(_$b), e === 'none' && (e = 'block'), ga[d] = e, e);
    }
    function ia(a, b) {
        for(var c, d, e = [], f = 0, g = a.length; f < g; f++)d = a[f], d.style && (c = d.style.display, b ? (c === 'none' && (e[f] = W.get(d, 'display') || null, e[f] || (d.style.display = '')), d.style.display === '' && da(d) && (e[f] = ha(d))) : c !== 'none' && (e[f] = 'none', W.set(d, 'display', c)));
        for(f = 0; f < g; f++)e[f] != null && (a[f].style.display = e[f]);
        return a;
    }
    r.fn.extend({
        show: function show() {
            return ia(this, !0);
        },
        hide: function hide() {
            return ia(this);
        },
        toggle: function toggle(a) {
            return typeof a == 'boolean' ? a ? this.show() : this.hide() : this.each(function() {
                da(this) ? r(this).show() : r(this).hide();
            });
        }
    });
    var ja = /^(?:checkbox|radio)$/i;
    var ka = /<([a-z][^\/\0>\x20\t\r\n\f]+)/i;
    var la = /^$|\/(?:java|ecma)script/i;
    var ma = {
        option: [
            1,
            "<select multiple='multiple'>",
            '</select>'
        ],
        thead: [
            1,
            '<table>',
            '</table>'
        ],
        col: [
            2,
            '<table><colgroup>',
            '</colgroup></table>'
        ],
        tr: [
            2,
            '<table><tbody>',
            '</tbody></table>'
        ],
        td: [
            3,
            '<table><tbody><tr>',
            '</tr></tbody></table>'
        ],
        _default: [
            0,
            '',
            ''
        ]
    };
    ma.optgroup = ma.option, ma.tbody = ma.tfoot = ma.colgroup = ma.caption = ma.thead, ma.th = ma.td;
    function na(a, b) {
        var c;
        return c = typeof a.getElementsByTagName != 'undefined' ? a.getElementsByTagName(b || '*') : typeof a.querySelectorAll != 'undefined' ? a.querySelectorAll(b || '*') : [], void 0 === b || b && B(a, b) ? r.merge([
            a
        ], c) : c;
    }
    function oa(a, b) {
        for(var c = 0, d = a.length; c < d; c++)W.set(a[c], 'globalEval', !b || W.get(b[c], 'globalEval'));
    }
    var pa = /<|&#?\w+;/;
    function qa(a, b, c, d, e) {
        for(var f, g, h, i, j, k, l = b.createDocumentFragment(), m = [], n = 0, o = a.length; n < o; n++)if (f = a[n], f || f === 0) if (r.type(f) === 'object') r.merge(m, f.nodeType ? [
            f
        ] : f);
        else if (pa.test(f)) {
            g = g || l.appendChild(b.createElement('div')), h = (ka.exec(f) || [
                '',
                ''
            ])[1].toLowerCase(), i = ma[h] || ma._default, g.innerHTML = i[1] + r.htmlPrefilter(f) + i[2], k = i[0];
            while(k--)g = g.lastChild;
            r.merge(m, g.childNodes), g = l.firstChild, g.textContent = '';
        } else m.push(b.createTextNode(f));
        l.textContent = '', n = 0;
        while(f = m[n++])if (d && r.inArray(f, d) > -1) e && e.push(f);
        else if (j = r.contains(f.ownerDocument, f), g = na(l.appendChild(f), 'script'), j && oa(g), c) {
            k = 0;
            while(f = g[k++])la.test(f.type || '') && c.push(f);
        }
        return l;
    }
    !function() {
        var _$a = d.createDocumentFragment();
        var _$b = _$a.appendChild(d.createElement('div'));
        var c = d.createElement('input');
        c.setAttribute('type', 'radio'), c.setAttribute('checked', 'checked'), c.setAttribute('name', 't'), _$b.appendChild(c), o.checkClone = _$b.cloneNode(!0).cloneNode(!0).lastChild.checked, _$b.innerHTML = '<textarea>x</textarea>', o.noCloneChecked = !!_$b.cloneNode(!0).lastChild.defaultValue;
    }();
    var ra = d.documentElement;
    var sa = /^key/;
    var ta = /^(?:mouse|pointer|contextmenu|drag|drop)|click/;
    var ua = /^([^.]*)(?:\.(.+)|)/;
    function va() {
        return !0;
    }
    function wa() {
        return !1;
    }
    function xa() {
        try {
            return d.activeElement;
        } catch (a) {}
    }
    function ya(a, b, c, d, e, f) {
        var g, h;
        if ((typeof b === "undefined" ? "undefined" : _type_of(b)) == 'object') {
            typeof c != 'string' && (d = d || c, c = void 0);
            for(h in b)ya(a, h, c, d, b[h], f);
            return a;
        }
        if (d == null && e == null ? (e = c, d = c = void 0) : e == null && (typeof c == 'string' ? (e = d, d = void 0) : (e = d, d = c, c = void 0)), e === !1) e = wa;
        else if (!e) return a;
        return f === 1 && (g = e, e = function e(a) {
            return r().off(a), g.apply(this, arguments);
        }, e.guid = g.guid || (g.guid = r.guid++)), a.each(function() {
            r.event.add(this, b, e, d, c);
        });
    }
    r.event = {
        global: {},
        add: function add(a, b, c, d, e) {
            var f;
            var g;
            var h;
            var i;
            var j;
            var k;
            var l;
            var m;
            var n;
            var o;
            var p;
            var q = W.get(a);
            if (q) {
                c.handler && (f = c, c = f.handler, e = f.selector), e && r.find.matchesSelector(ra, e), c.guid || (c.guid = r.guid++), (i = q.events) || (i = q.events = {}), (g = q.handle) || (g = q.handle = function g(b) {
                    return typeof r != 'undefined' && r.event.triggered !== b.type ? r.event.dispatch.apply(a, arguments) : void 0;
                }), b = (b || '').match(L) || [
                    ''
                ], j = b.length;
                while(j--)h = ua.exec(b[j]) || [], n = p = h[1], o = (h[2] || '').split('.').sort(), n && (l = r.event.special[n] || {}, n = (e ? l.delegateType : l.bindType) || n, l = r.event.special[n] || {}, k = r.extend({
                    type: n,
                    origType: p,
                    data: d,
                    handler: c,
                    guid: c.guid,
                    selector: e,
                    needsContext: e && r.expr.match.needsContext.test(e),
                    namespace: o.join('.')
                }, f), (m = i[n]) || (m = i[n] = [], m.delegateCount = 0, l.setup && l.setup.call(a, d, o, g) !== !1 || a.addEventListener && a.addEventListener(n, g)), l.add && (l.add.call(a, k), k.handler.guid || (k.handler.guid = c.guid)), e ? m.splice(m.delegateCount++, 0, k) : m.push(k), r.event.global[n] = !0);
            }
        },
        remove: function remove(a, b, c, d, e) {
            var f;
            var g;
            var h;
            var i;
            var j;
            var k;
            var l;
            var m;
            var n;
            var o;
            var p;
            var q = W.hasData(a) && W.get(a);
            if (q && (i = q.events)) {
                b = (b || '').match(L) || [
                    ''
                ], j = b.length;
                while(j--)if (h = ua.exec(b[j]) || [], n = p = h[1], o = (h[2] || '').split('.').sort(), n) {
                    l = r.event.special[n] || {}, n = (d ? l.delegateType : l.bindType) || n, m = i[n] || [], h = h[2] && new RegExp('(^|\\.)' + o.join('\\.(?:.*\\.|)') + '(\\.|$)'), g = f = m.length;
                    while(f--)k = m[f], !e && p !== k.origType || c && c.guid !== k.guid || h && !h.test(k.namespace) || d && d !== k.selector && (d !== '**' || !k.selector) || (m.splice(f, 1), k.selector && m.delegateCount--, l.remove && l.remove.call(a, k));
                    g && !m.length && (l.teardown && l.teardown.call(a, o, q.handle) !== !1 || r.removeEvent(a, n, q.handle), delete i[n]);
                } else for(n in i)r.event.remove(a, n + b[j], c, d, !0);
                r.isEmptyObject(i) && W.remove(a, 'handle events');
            }
        },
        dispatch: function dispatch(a) {
            var _$b = r.event.fix(a);
            var c;
            var d;
            var e;
            var f;
            var g;
            var h;
            var i = new Array(arguments.length);
            var j = (W.get(this, 'events') || {})[_$b.type] || [];
            var k = r.event.special[_$b.type] || {};
            for(i[0] = _$b, c = 1; c < arguments.length; c++)i[c] = arguments[c];
            if (_$b.delegateTarget = this, !k.preDispatch || k.preDispatch.call(this, _$b) !== !1) {
                h = r.event.handlers.call(this, _$b, j), c = 0;
                while((f = h[c++]) && !_$b.isPropagationStopped()){
                    _$b.currentTarget = f.elem, d = 0;
                    while((g = f.handlers[d++]) && !_$b.isImmediatePropagationStopped())_$b.rnamespace && !_$b.rnamespace.test(g.namespace) || (_$b.handleObj = g, _$b.data = g.data, e = ((r.event.special[g.origType] || {}).handle || g.handler).apply(f.elem, i), void 0 !== e && (_$b.result = e) === !1 && (_$b.preventDefault(), _$b.stopPropagation()));
                }
                return k.postDispatch && k.postDispatch.call(this, _$b), _$b.result;
            }
        },
        handlers: function handlers(a, b) {
            var c;
            var d;
            var e;
            var f;
            var g;
            var h = [];
            var i = b.delegateCount;
            var j = a.target;
            if (i && j.nodeType && !(a.type === 'click' && a.button >= 1)) {
                for(; j !== this; j = j.parentNode || this)if (j.nodeType === 1 && (a.type !== 'click' || j.disabled !== !0)) {
                    for(f = [], g = {}, c = 0; c < i; c++)d = b[c], e = d.selector + ' ', void 0 === g[e] && (g[e] = d.needsContext ? r(e, this).index(j) > -1 : r.find(e, this, null, [
                        j
                    ]).length), g[e] && f.push(d);
                    f.length && h.push({
                        elem: j,
                        handlers: f
                    });
                }
            }
            return j = this, i < b.length && h.push({
                elem: j,
                handlers: b.slice(i)
            }), h;
        },
        addProp: function addProp(a, b) {
            Object.defineProperty(r.Event.prototype, a, {
                enumerable: !0,
                configurable: !0,
                get: r.isFunction(b) ? function() {
                    if (this.originalEvent) return b(this.originalEvent);
                } : function() {
                    if (this.originalEvent) return this.originalEvent[a];
                },
                set: function set(b) {
                    Object.defineProperty(this, a, {
                        enumerable: !0,
                        configurable: !0,
                        writable: !0,
                        value: b
                    });
                }
            });
        },
        fix: function fix(a) {
            return a[r.expando] ? a : new r.Event(a);
        },
        special: {
            load: {
                noBubble: !0
            },
            focus: {
                trigger: function trigger() {
                    if (this !== xa() && this.focus) return this.focus(), !1;
                },
                delegateType: 'focusin'
            },
            blur: {
                trigger: function trigger() {
                    if (this === xa() && this.blur) return this.blur(), !1;
                },
                delegateType: 'focusout'
            },
            click: {
                trigger: function trigger() {
                    if (this.type === 'checkbox' && this.click && B(this, 'input')) return this.click(), !1;
                },
                _default: function _default(a) {
                    return B(a.target, 'a');
                }
            },
            beforeunload: {
                postDispatch: function postDispatch(a) {
                    void 0 !== a.result && a.originalEvent && (a.originalEvent.returnValue = a.result);
                }
            }
        }
    }, r.removeEvent = function(a, b, c) {
        a.removeEventListener && a.removeEventListener(b, c);
    }, r.Event = function(a, b) {
        return _instanceof(this, r.Event) ? (a && a.type ? (this.originalEvent = a, this.type = a.type, this.isDefaultPrevented = a.defaultPrevented || void 0 === a.defaultPrevented && a.returnValue === !1 ? va : wa, this.target = a.target && a.target.nodeType === 3 ? a.target.parentNode : a.target, this.currentTarget = a.currentTarget, this.relatedTarget = a.relatedTarget) : this.type = a, b && r.extend(this, b), this.timeStamp = a && a.timeStamp || r.now(), void (this[r.expando] = !0)) : new r.Event(a, b);
    }, r.Event.prototype = {
        constructor: r.Event,
        isDefaultPrevented: wa,
        isPropagationStopped: wa,
        isImmediatePropagationStopped: wa,
        isSimulated: !1,
        preventDefault: function preventDefault() {
            var _$a = this.originalEvent;
            this.isDefaultPrevented = va, _$a && !this.isSimulated && _$a.preventDefault();
        },
        stopPropagation: function stopPropagation() {
            var _$a = this.originalEvent;
            this.isPropagationStopped = va, _$a && !this.isSimulated && _$a.stopPropagation();
        },
        stopImmediatePropagation: function stopImmediatePropagation() {
            var _$a = this.originalEvent;
            this.isImmediatePropagationStopped = va, _$a && !this.isSimulated && _$a.stopImmediatePropagation(), this.stopPropagation();
        }
    }, r.each({
        altKey: !0,
        bubbles: !0,
        cancelable: !0,
        changedTouches: !0,
        ctrlKey: !0,
        detail: !0,
        eventPhase: !0,
        metaKey: !0,
        pageX: !0,
        pageY: !0,
        shiftKey: !0,
        view: !0,
        char: !0,
        charCode: !0,
        key: !0,
        keyCode: !0,
        button: !0,
        buttons: !0,
        clientX: !0,
        clientY: !0,
        offsetX: !0,
        offsetY: !0,
        pointerId: !0,
        pointerType: !0,
        screenX: !0,
        screenY: !0,
        targetTouches: !0,
        toElement: !0,
        touches: !0,
        which: function which(a) {
            var _$b = a.button;
            return a.which == null && sa.test(a.type) ? a.charCode != null ? a.charCode : a.keyCode : !a.which && void 0 !== _$b && ta.test(a.type) ? 1 & _$b ? 1 : 2 & _$b ? 3 : 4 & _$b ? 2 : 0 : a.which;
        }
    }, r.event.addProp), r.each({
        mouseenter: 'mouseover',
        mouseleave: 'mouseout',
        pointerenter: 'pointerover',
        pointerleave: 'pointerout'
    }, function(a, b) {
        r.event.special[a] = {
            delegateType: b,
            bindType: b,
            handle: function handle(a) {
                var c;
                var d = this;
                var e = a.relatedTarget;
                var f = a.handleObj;
                return e && (e === d || r.contains(d, e)) || (a.type = f.origType, c = f.handler.apply(this, arguments), a.type = b), c;
            }
        };
    }), r.fn.extend({
        on: function on(a, b, c, d) {
            return ya(this, a, b, c, d);
        },
        one: function one(a, b, c, d) {
            return ya(this, a, b, c, d, 1);
        },
        off: function off(a, b, c) {
            var d, e;
            if (a && a.preventDefault && a.handleObj) return d = a.handleObj, r(a.delegateTarget).off(d.namespace ? d.origType + '.' + d.namespace : d.origType, d.selector, d.handler), this;
            if ((typeof a === "undefined" ? "undefined" : _type_of(a)) == 'object') {
                for(e in a)this.off(e, b, a[e]);
                return this;
            }
            return b !== !1 && typeof b != 'function' || (c = b, b = void 0), c === !1 && (c = wa), this.each(function() {
                r.event.remove(this, a, c, b);
            });
        }
    });
    var za = /<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi;
    var Aa = /<script|<style|<link/i;
    var Ba = /checked\s*(?:[^=]|=\s*.checked.)/i;
    var Ca = /^true\/(.*)/;
    var Da = /^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;
    function Ea(a, b) {
        return B(a, 'table') && B(b.nodeType !== 11 ? b : b.firstChild, 'tr') ? r('>tbody', a)[0] || a : a;
    }
    function Fa(a) {
        return a.type = (a.getAttribute('type') !== null) + '/' + a.type, a;
    }
    function Ga(a) {
        var _$b = Ca.exec(a.type);
        return _$b ? a.type = _$b[1] : a.removeAttribute('type'), a;
    }
    function Ha(a, b) {
        var c, d, e, f, g, h, i, j;
        if (b.nodeType === 1) {
            if (W.hasData(a) && (f = W.access(a), g = W.set(b, f), j = f.events)) {
                delete g.handle, g.events = {};
                for(e in j)for(c = 0, d = j[e].length; c < d; c++)r.event.add(b, e, j[e][c]);
            }
            X.hasData(a) && (h = X.access(a), i = r.extend({}, h), X.set(b, i));
        }
    }
    function Ia(a, b) {
        var c = b.nodeName.toLowerCase();
        c === 'input' && ja.test(a.type) ? b.checked = a.checked : c !== 'input' && c !== 'textarea' || (b.defaultValue = a.defaultValue);
    }
    function Ja(a, b, c, d) {
        b = g.apply([], b);
        var e;
        var f;
        var h;
        var i;
        var j;
        var k;
        var l = 0;
        var m = a.length;
        var n = m - 1;
        var q = b[0];
        var s = r.isFunction(q);
        if (s || m > 1 && typeof q == 'string' && !o.checkClone && Ba.test(q)) return a.each(function(e) {
            var f = a.eq(e);
            s && (b[0] = q.call(this, e, f.html())), Ja(f, b, c, d);
        });
        if (m && (e = qa(b, a[0].ownerDocument, !1, a, d), f = e.firstChild, e.childNodes.length === 1 && (e = f), f || d)) {
            for(h = r.map(na(e, 'script'), Fa), i = h.length; l < m; l++)j = e, l !== n && (j = r.clone(j, !0, !0), i && r.merge(h, na(j, 'script'))), c.call(a[l], j, l);
            if (i) for(k = h[h.length - 1].ownerDocument, r.map(h, Ga), l = 0; l < i; l++)j = h[l], la.test(j.type || '') && !W.access(j, 'globalEval') && r.contains(k, j) && (j.src ? r._evalUrl && r._evalUrl(j.src) : p(j.textContent.replace(Da, ''), k));
        }
        return a;
    }
    function Ka(a, b, c) {
        for(var d, e = b ? r.filter(b, a) : a, f = 0; (d = e[f]) != null; f++)c || d.nodeType !== 1 || r.cleanData(na(d)), d.parentNode && (c && r.contains(d.ownerDocument, d) && oa(na(d, 'script')), d.parentNode.removeChild(d));
        return a;
    }
    r.extend({
        htmlPrefilter: function htmlPrefilter(a) {
            return a.replace(za, '<$1></$2>');
        },
        clone: function clone(a, b, c) {
            var d;
            var e;
            var f;
            var g;
            var h = a.cloneNode(!0);
            var i = r.contains(a.ownerDocument, a);
            if (!(o.noCloneChecked || a.nodeType !== 1 && a.nodeType !== 11 || r.isXMLDoc(a))) for(g = na(h), f = na(a), d = 0, e = f.length; d < e; d++)Ia(f[d], g[d]);
            if (b) if (c) for(f = f || na(a), g = g || na(h), d = 0, e = f.length; d < e; d++)Ha(f[d], g[d]);
            else Ha(a, h);
            return g = na(h, 'script'), g.length > 0 && oa(g, !i && na(a, 'script')), h;
        },
        cleanData: function cleanData(a) {
            for(var _$b, c, d, e = r.event.special, f = 0; void 0 !== (c = a[f]); f++)if (U(c)) {
                if (_$b = c[W.expando]) {
                    if (_$b.events) for(d in _$b.events)e[d] ? r.event.remove(c, d) : r.removeEvent(c, d, _$b.handle);
                    c[W.expando] = void 0;
                }
                c[X.expando] && (c[X.expando] = void 0);
            }
        }
    }), r.fn.extend({
        detach: function detach(a) {
            return Ka(this, a, !0);
        },
        remove: function remove(a) {
            return Ka(this, a);
        },
        text: function text(a) {
            return T(this, function(a) {
                return void 0 === a ? r.text(this) : this.empty().each(function() {
                    this.nodeType !== 1 && this.nodeType !== 11 && this.nodeType !== 9 || (this.textContent = a);
                });
            }, null, a, arguments.length);
        },
        append: function append() {
            return Ja(this, arguments, function(a) {
                if (this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9) {
                    var _$b = Ea(this, a);
                    _$b.appendChild(a);
                }
            });
        },
        prepend: function prepend() {
            return Ja(this, arguments, function(a) {
                if (this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9) {
                    var _$b = Ea(this, a);
                    _$b.insertBefore(a, _$b.firstChild);
                }
            });
        },
        before: function before() {
            return Ja(this, arguments, function(a) {
                this.parentNode && this.parentNode.insertBefore(a, this);
            });
        },
        after: function after() {
            return Ja(this, arguments, function(a) {
                this.parentNode && this.parentNode.insertBefore(a, this.nextSibling);
            });
        },
        empty: function empty() {
            for(var _$a, _$b = 0; (_$a = this[_$b]) != null; _$b++)_$a.nodeType === 1 && (r.cleanData(na(_$a, !1)), _$a.textContent = '');
            return this;
        },
        clone: function clone(a, b) {
            return a = a != null && a, b = b == null ? a : b, this.map(function() {
                return r.clone(this, a, b);
            });
        },
        html: function html(a) {
            return T(this, function(a) {
                var _$b = this[0] || {};
                var c = 0;
                var d = this.length;
                if (void 0 === a && _$b.nodeType === 1) return _$b.innerHTML;
                if (typeof a == 'string' && !Aa.test(a) && !ma[(ka.exec(a) || [
                    '',
                    ''
                ])[1].toLowerCase()]) {
                    a = r.htmlPrefilter(a);
                    try {
                        for(; c < d; c++)_$b = this[c] || {}, _$b.nodeType === 1 && (r.cleanData(na(_$b, !1)), _$b.innerHTML = a);
                        _$b = 0;
                    } catch (e) {}
                }
                _$b && this.empty().append(a);
            }, null, a, arguments.length);
        },
        replaceWith: function replaceWith() {
            var _$a = [];
            return Ja(this, arguments, function(b) {
                var c = this.parentNode;
                r.inArray(this, _$a) < 0 && (r.cleanData(na(this)), c && c.replaceChild(b, this));
            }, _$a);
        }
    }), r.each({
        appendTo: 'append',
        prependTo: 'prepend',
        insertBefore: 'before',
        insertAfter: 'after',
        replaceAll: 'replaceWith'
    }, function(a, b) {
        r.fn[a] = function(a) {
            for(var c, d = [], e = r(a), f = e.length - 1, g = 0; g <= f; g++)c = g === f ? this : this.clone(!0), r(e[g])[b](c), h.apply(d, c.get());
            return this.pushStack(d);
        };
    });
    var La = /^margin/;
    var Ma = new RegExp('^(' + aa + ')(?!px)[a-z%]+$', 'i');
    var Na = function Na(b) {
        var c = b.ownerDocument.defaultView;
        return c && c.opener || (c = a), c.getComputedStyle(b);
    };
    !function() {
        function b() {
            if (i) {
                i.style.cssText = 'box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%', i.innerHTML = '', ra.appendChild(h);
                var _$b = a.getComputedStyle(i);
                c = _$b.top !== '1%', g = _$b.marginLeft === '2px', e = _$b.width === '4px', i.style.marginRight = '50%', f = _$b.marginRight === '4px', ra.removeChild(h), i = null;
            }
        }
        var c;
        var e;
        var f;
        var g;
        var h = d.createElement('div');
        var i = d.createElement('div');
        i.style && (i.style.backgroundClip = 'content-box', i.cloneNode(!0).style.backgroundClip = '', o.clearCloneStyle = i.style.backgroundClip === 'content-box', h.style.cssText = 'border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute', h.appendChild(i), r.extend(o, {
            pixelPosition: function pixelPosition() {
                return b(), c;
            },
            boxSizingReliable: function boxSizingReliable() {
                return b(), e;
            },
            pixelMarginRight: function pixelMarginRight() {
                return b(), f;
            },
            reliableMarginLeft: function reliableMarginLeft() {
                return b(), g;
            }
        }));
    }();
    function Oa(a, b, c) {
        var d;
        var e;
        var f;
        var g;
        var h = a.style;
        return c = c || Na(a), c && (g = c.getPropertyValue(b) || c[b], g !== '' || r.contains(a.ownerDocument, a) || (g = r.style(a, b)), !o.pixelMarginRight() && Ma.test(g) && La.test(b) && (d = h.width, e = h.minWidth, f = h.maxWidth, h.minWidth = h.maxWidth = h.width = g, g = c.width, h.width = d, h.minWidth = e, h.maxWidth = f)), void 0 !== g ? g + '' : g;
    }
    function Pa(a, b) {
        return {
            get: function get() {
                return a() ? void delete this.get : (this.get = b).apply(this, arguments);
            }
        };
    }
    var Qa = /^(none|table(?!-c[ea]).+)/;
    var Ra = /^--/;
    var Sa = {
        position: 'absolute',
        visibility: 'hidden',
        display: 'block'
    };
    var Ta = {
        letterSpacing: '0',
        fontWeight: '400'
    };
    var Ua = [
        'Webkit',
        'Moz',
        'ms'
    ];
    var Va = d.createElement('div').style;
    function Wa(a) {
        if (a in Va) return a;
        var _$b = a[0].toUpperCase() + a.slice(1);
        var c = Ua.length;
        while(c--)if (a = Ua[c] + _$b, a in Va) return a;
    }
    function Xa(a) {
        var _$b = r.cssProps[a];
        return _$b || (_$b = r.cssProps[a] = Wa(a) || a), _$b;
    }
    function Ya(a, b, c) {
        var d = ba.exec(b);
        return d ? Math.max(0, d[2] - (c || 0)) + (d[3] || 'px') : b;
    }
    function Za(a, b, c, d, e) {
        var f;
        var g = 0;
        for(f = c === (d ? 'border' : 'content') ? 4 : b === 'width' ? 1 : 0; f < 4; f += 2)c === 'margin' && (g += r.css(a, c + ca[f], !0, e)), d ? (c === 'content' && (g -= r.css(a, 'padding' + ca[f], !0, e)), c !== 'margin' && (g -= r.css(a, 'border' + ca[f] + 'Width', !0, e))) : (g += r.css(a, 'padding' + ca[f], !0, e), c !== 'padding' && (g += r.css(a, 'border' + ca[f] + 'Width', !0, e)));
        return g;
    }
    function $a(a, b, c) {
        var d;
        var e = Na(a);
        var f = Oa(a, b, e);
        var g = r.css(a, 'boxSizing', !1, e) === 'border-box';
        return Ma.test(f) ? f : (d = g && (o.boxSizingReliable() || f === a.style[b]), f === 'auto' && (f = a['offset' + b[0].toUpperCase() + b.slice(1)]), f = parseFloat(f) || 0, f + Za(a, b, c || (g ? 'border' : 'content'), d, e) + 'px');
    }
    r.extend({
        cssHooks: {
            opacity: {
                get: function get(a, b) {
                    if (b) {
                        var c = Oa(a, 'opacity');
                        return c === '' ? '1' : c;
                    }
                }
            }
        },
        cssNumber: {
            animationIterationCount: !0,
            columnCount: !0,
            fillOpacity: !0,
            flexGrow: !0,
            flexShrink: !0,
            fontWeight: !0,
            lineHeight: !0,
            opacity: !0,
            order: !0,
            orphans: !0,
            widows: !0,
            zIndex: !0,
            zoom: !0
        },
        cssProps: {
            float: 'cssFloat'
        },
        style: function style(a, b, c, d) {
            if (a && a.nodeType !== 3 && a.nodeType !== 8 && a.style) {
                var e;
                var f;
                var g;
                var h = r.camelCase(b);
                var i = Ra.test(b);
                var j = a.style;
                return i || (b = Xa(h)), g = r.cssHooks[b] || r.cssHooks[h], void 0 === c ? g && 'get' in g && void 0 !== (e = g.get(a, !1, d)) ? e : j[b] : (f = typeof c === "undefined" ? "undefined" : _type_of(c), f === 'string' && (e = ba.exec(c)) && e[1] && (c = fa(a, b, e), f = 'number'), c != null && c === c && (f === 'number' && (c += e && e[3] || (r.cssNumber[h] ? '' : 'px')), o.clearCloneStyle || c !== '' || b.indexOf('background') !== 0 || (j[b] = 'inherit'), g && 'set' in g && void 0 === (c = g.set(a, c, d)) || (i ? j.setProperty(b, c) : j[b] = c)), void 0);
            }
        },
        css: function css(a, b, c, d) {
            var e;
            var f;
            var g;
            var h = r.camelCase(b);
            var i = Ra.test(b);
            return i || (b = Xa(h)), g = r.cssHooks[b] || r.cssHooks[h], g && 'get' in g && (e = g.get(a, !0, c)), void 0 === e && (e = Oa(a, b, d)), e === 'normal' && b in Ta && (e = Ta[b]), c === '' || c ? (f = parseFloat(e), c === !0 || isFinite(f) ? f || 0 : e) : e;
        }
    }), r.each([
        'height',
        'width'
    ], function(a, b) {
        r.cssHooks[b] = {
            get: function get(a, c, d) {
                if (c) return !Qa.test(r.css(a, 'display')) || a.getClientRects().length && a.getBoundingClientRect().width ? $a(a, b, d) : ea(a, Sa, function() {
                    return $a(a, b, d);
                });
            },
            set: function set(a, c, d) {
                var e;
                var f = d && Na(a);
                var g = d && Za(a, b, d, r.css(a, 'boxSizing', !1, f) === 'border-box', f);
                return g && (e = ba.exec(c)) && (e[3] || 'px') !== 'px' && (a.style[b] = c, c = r.css(a, b)), Ya(a, c, g);
            }
        };
    }), r.cssHooks.marginLeft = Pa(o.reliableMarginLeft, function(a, b) {
        if (b) return (parseFloat(Oa(a, 'marginLeft')) || a.getBoundingClientRect().left - ea(a, {
            marginLeft: 0
        }, function() {
            return a.getBoundingClientRect().left;
        })) + 'px';
    }), r.each({
        margin: '',
        padding: '',
        border: 'Width'
    }, function(a, b) {
        r.cssHooks[a + b] = {
            expand: function expand(c) {
                for(var d = 0, e = {}, f = typeof c == 'string' ? c.split(' ') : [
                    c
                ]; d < 4; d++)e[a + ca[d] + b] = f[d] || f[d - 2] || f[0];
                return e;
            }
        }, La.test(a) || (r.cssHooks[a + b].set = Ya);
    }), r.fn.extend({
        css: function css(a, b) {
            return T(this, function(a, b, c) {
                var d;
                var e;
                var f = {};
                var g = 0;
                if (Array.isArray(b)) {
                    for(d = Na(a), e = b.length; g < e; g++)f[b[g]] = r.css(a, b[g], !1, d);
                    return f;
                }
                return void 0 !== c ? r.style(a, b, c) : r.css(a, b);
            }, a, b, arguments.length > 1);
        }
    });
    function _a(a, b, c, d, e) {
        return new _a.prototype.init(a, b, c, d, e);
    }
    r.Tween = _a, _a.prototype = {
        constructor: _a,
        init: function init(a, b, c, d, e, f) {
            this.elem = a, this.prop = c, this.easing = e || r.easing._default, this.options = b, this.start = this.now = this.cur(), this.end = d, this.unit = f || (r.cssNumber[c] ? '' : 'px');
        },
        cur: function cur() {
            var _$a = _a.propHooks[this.prop];
            return _$a && _$a.get ? _$a.get(this) : _a.propHooks._default.get(this);
        },
        run: function run(a) {
            var _$b;
            var c = _a.propHooks[this.prop];
            return this.options.duration ? this.pos = _$b = r.easing[this.easing](a, this.options.duration * a, 0, 1, this.options.duration) : this.pos = _$b = a, this.now = (this.end - this.start) * _$b + this.start, this.options.step && this.options.step.call(this.elem, this.now, this), c && c.set ? c.set(this) : _a.propHooks._default.set(this), this;
        }
    }, _a.prototype.init.prototype = _a.prototype, _a.propHooks = {
        _default: {
            get: function get(a) {
                var _$b;
                return a.elem.nodeType !== 1 || a.elem[a.prop] != null && a.elem.style[a.prop] == null ? a.elem[a.prop] : (_$b = r.css(a.elem, a.prop, ''), _$b && _$b !== 'auto' ? _$b : 0);
            },
            set: function set(a) {
                r.fx.step[a.prop] ? r.fx.step[a.prop](a) : a.elem.nodeType !== 1 || a.elem.style[r.cssProps[a.prop]] == null && !r.cssHooks[a.prop] ? a.elem[a.prop] = a.now : r.style(a.elem, a.prop, a.now + a.unit);
            }
        }
    }, _a.propHooks.scrollTop = _a.propHooks.scrollLeft = {
        set: function set(a) {
            a.elem.nodeType && a.elem.parentNode && (a.elem[a.prop] = a.now);
        }
    }, r.easing = {
        linear: function linear(a) {
            return a;
        },
        swing: function swing(a) {
            return 0.5 - Math.cos(a * Math.PI) / 2;
        },
        _default: 'swing'
    }, r.fx = _a.prototype.init, r.fx.step = {};
    var ab;
    var bb;
    var cb = /^(?:toggle|show|hide)$/;
    var db = /queueHooks$/;
    function eb() {
        bb && (d.hidden === !1 && a.requestAnimationFrame ? a.requestAnimationFrame(eb) : a.setTimeout(eb, r.fx.interval), r.fx.tick());
    }
    function fb() {
        return a.setTimeout(function() {
            ab = void 0;
        }), ab = r.now();
    }
    function gb(a, b) {
        var c;
        var d = 0;
        var e = {
            height: a
        };
        for(b = b ? 1 : 0; d < 4; d += 2 - b)c = ca[d], e['margin' + c] = e['padding' + c] = a;
        return b && (e.opacity = e.width = a), e;
    }
    function hb(a, b, c) {
        for(var d, e = (kb.tweeners[b] || []).concat(kb.tweeners['*']), f = 0, g = e.length; f < g; f++)if (d = e[f].call(c, b, a)) return d;
    }
    function ib(a, b, c) {
        var d;
        var e;
        var f;
        var g;
        var h;
        var i;
        var j;
        var k;
        var l = 'width' in b || 'height' in b;
        var m = this;
        var n = {};
        var o = a.style;
        var p = a.nodeType && da(a);
        var q = W.get(a, 'fxshow');
        c.queue || (g = r._queueHooks(a, 'fx'), g.unqueued == null && (g.unqueued = 0, h = g.empty.fire, g.empty.fire = function() {
            g.unqueued || h();
        }), g.unqueued++, m.always(function() {
            m.always(function() {
                g.unqueued--, r.queue(a, 'fx').length || g.empty.fire();
            });
        }));
        for(d in b)if (e = b[d], cb.test(e)) {
            if (delete b[d], f = f || e === 'toggle', e === (p ? 'hide' : 'show')) {
                if (e !== 'show' || !q || void 0 === q[d]) continue;
                p = !0;
            }
            n[d] = q && q[d] || r.style(a, d);
        }
        if (i = !r.isEmptyObject(b), i || !r.isEmptyObject(n)) {
            l && a.nodeType === 1 && (c.overflow = [
                o.overflow,
                o.overflowX,
                o.overflowY
            ], j = q && q.display, j == null && (j = W.get(a, 'display')), k = r.css(a, 'display'), k === 'none' && (j ? k = j : (ia([
                a
            ], !0), j = a.style.display || j, k = r.css(a, 'display'), ia([
                a
            ]))), (k === 'inline' || k === 'inline-block' && j != null) && r.css(a, 'float') === 'none' && (i || (m.done(function() {
                o.display = j;
            }), j == null && (k = o.display, j = k === 'none' ? '' : k)), o.display = 'inline-block')), c.overflow && (o.overflow = 'hidden', m.always(function() {
                o.overflow = c.overflow[0], o.overflowX = c.overflow[1], o.overflowY = c.overflow[2];
            })), i = !1;
            for(d in n)i || (q ? 'hidden' in q && (p = q.hidden) : q = W.access(a, 'fxshow', {
                display: j
            }), f && (q.hidden = !p), p && ia([
                a
            ], !0), m.done(function() {
                p || ia([
                    a
                ]), W.remove(a, 'fxshow');
                for(d in n)r.style(a, d, n[d]);
            })), i = hb(p ? q[d] : 0, d, m), d in q || (q[d] = i.start, p && (i.end = i.start, i.start = 0));
        }
    }
    function jb(a, b) {
        var c, d, e, f, g;
        for(c in a)if (d = r.camelCase(c), e = b[d], f = a[c], Array.isArray(f) && (e = f[1], f = a[c] = f[0]), c !== d && (a[d] = f, delete a[c]), g = r.cssHooks[d], g && 'expand' in g) {
            f = g.expand(f), delete a[d];
            for(c in f)c in a || (a[c] = f[c], b[c] = e);
        } else b[d] = e;
    }
    function kb(a, b, c) {
        var d;
        var e;
        var f = 0;
        var g = kb.prefilters.length;
        var h = r.Deferred().always(function() {
            delete i.elem;
        });
        var i = function i() {
            if (e) return !1;
            for(var _$b = ab || fb(), _$c = Math.max(0, j.startTime + j.duration - _$b), d = _$c / j.duration || 0, f = 1 - d, g = 0, i = j.tweens.length; g < i; g++)j.tweens[g].run(f);
            return h.notifyWith(a, [
                j,
                f,
                _$c
            ]), f < 1 && i ? _$c : (i || h.notifyWith(a, [
                j,
                1,
                0
            ]), h.resolveWith(a, [
                j
            ]), !1);
        };
        var j = h.promise({
            elem: a,
            props: r.extend({}, b),
            opts: r.extend(!0, {
                specialEasing: {},
                easing: r.easing._default
            }, c),
            originalProperties: b,
            originalOptions: c,
            startTime: ab || fb(),
            duration: c.duration,
            tweens: [],
            createTween: function createTween(b, c) {
                var d = r.Tween(a, j.opts, b, c, j.opts.specialEasing[b] || j.opts.easing);
                return j.tweens.push(d), d;
            },
            stop: function stop(b) {
                var _$c = 0;
                var d = b ? j.tweens.length : 0;
                if (e) return this;
                for(e = !0; _$c < d; _$c++)j.tweens[_$c].run(1);
                return b ? (h.notifyWith(a, [
                    j,
                    1,
                    0
                ]), h.resolveWith(a, [
                    j,
                    b
                ])) : h.rejectWith(a, [
                    j,
                    b
                ]), this;
            }
        });
        var k = j.props;
        for(jb(k, j.opts.specialEasing); f < g; f++)if (d = kb.prefilters[f].call(j, a, k, j.opts)) return r.isFunction(d.stop) && (r._queueHooks(j.elem, j.opts.queue).stop = r.proxy(d.stop, d)), d;
        return r.map(k, hb, j), r.isFunction(j.opts.start) && j.opts.start.call(a, j), j.progress(j.opts.progress).done(j.opts.done, j.opts.complete).fail(j.opts.fail).always(j.opts.always), r.fx.timer(r.extend(i, {
            elem: a,
            anim: j,
            queue: j.opts.queue
        })), j;
    }
    r.Animation = r.extend(kb, {
        tweeners: {
            '*': [
                function(a, b) {
                    var c = this.createTween(a, b);
                    return fa(c.elem, a, ba.exec(b), c), c;
                }
            ]
        },
        tweener: function tweener(a, b) {
            r.isFunction(a) ? (b = a, a = [
                '*'
            ]) : a = a.match(L);
            for(var c, d = 0, e = a.length; d < e; d++)c = a[d], kb.tweeners[c] = kb.tweeners[c] || [], kb.tweeners[c].unshift(b);
        },
        prefilters: [
            ib
        ],
        prefilter: function prefilter(a, b) {
            b ? kb.prefilters.unshift(a) : kb.prefilters.push(a);
        }
    }), r.speed = function(a, b, c) {
        var d = a && (typeof a === "undefined" ? "undefined" : _type_of(a)) == 'object' ? r.extend({}, a) : {
            complete: c || !c && b || r.isFunction(a) && a,
            duration: a,
            easing: c && b || b && !r.isFunction(b) && b
        };
        return r.fx.off ? d.duration = 0 : typeof d.duration != 'number' && (d.duration in r.fx.speeds ? d.duration = r.fx.speeds[d.duration] : d.duration = r.fx.speeds._default), d.queue != null && d.queue !== !0 || (d.queue = 'fx'), d.old = d.complete, d.complete = function() {
            r.isFunction(d.old) && d.old.call(this), d.queue && r.dequeue(this, d.queue);
        }, d;
    }, r.fn.extend({
        fadeTo: function fadeTo(a, b, c, d) {
            return this.filter(da).css('opacity', 0).show().end().animate({
                opacity: b
            }, a, c, d);
        },
        animate: function animate(a, b, c, d) {
            var e = r.isEmptyObject(a);
            var f = r.speed(b, c, d);
            var g = function g() {
                var _$b = kb(this, r.extend({}, a), f);
                (e || W.get(this, 'finish')) && _$b.stop(!0);
            };
            return g.finish = g, e || f.queue === !1 ? this.each(g) : this.queue(f.queue, g);
        },
        stop: function stop(a, b, c) {
            var d = function d(a) {
                var _$b = a.stop;
                delete a.stop, _$b(c);
            };
            return typeof a != 'string' && (c = b, b = a, a = void 0), b && a !== !1 && this.queue(a || 'fx', []), this.each(function() {
                var _$b = !0;
                var e = a != null && a + 'queueHooks';
                var f = r.timers;
                var g = W.get(this);
                if (e) g[e] && g[e].stop && d(g[e]);
                else for(e in g)g[e] && g[e].stop && db.test(e) && d(g[e]);
                for(e = f.length; e--;)f[e].elem !== this || a != null && f[e].queue !== a || (f[e].anim.stop(c), _$b = !1, f.splice(e, 1));
                !_$b && c || r.dequeue(this, a);
            });
        },
        finish: function finish(a) {
            return a !== !1 && (a = a || 'fx'), this.each(function() {
                var _$b;
                var c = W.get(this);
                var d = c[a + 'queue'];
                var e = c[a + 'queueHooks'];
                var f = r.timers;
                var g = d ? d.length : 0;
                for(c.finish = !0, r.queue(this, a, []), e && e.stop && e.stop.call(this, !0), _$b = f.length; _$b--;)f[_$b].elem === this && f[_$b].queue === a && (f[_$b].anim.stop(!0), f.splice(_$b, 1));
                for(_$b = 0; _$b < g; _$b++)d[_$b] && d[_$b].finish && d[_$b].finish.call(this);
                delete c.finish;
            });
        }
    }), r.each([
        'toggle',
        'show',
        'hide'
    ], function(a, b) {
        var c = r.fn[b];
        r.fn[b] = function(a, d, e) {
            return a == null || typeof a == 'boolean' ? c.apply(this, arguments) : this.animate(gb(b, !0), a, d, e);
        };
    }), r.each({
        slideDown: gb('show'),
        slideUp: gb('hide'),
        slideToggle: gb('toggle'),
        fadeIn: {
            opacity: 'show'
        },
        fadeOut: {
            opacity: 'hide'
        },
        fadeToggle: {
            opacity: 'toggle'
        }
    }, function(a, b) {
        r.fn[a] = function(a, c, d) {
            return this.animate(b, a, c, d);
        };
    }), r.timers = [], r.fx.tick = function() {
        var _$a;
        var _$b = 0;
        var c = r.timers;
        for(ab = r.now(); _$b < c.length; _$b++)_$a = c[_$b], _$a() || c[_$b] !== _$a || c.splice(_$b--, 1);
        c.length || r.fx.stop(), ab = void 0;
    }, r.fx.timer = function(a) {
        r.timers.push(a), r.fx.start();
    }, r.fx.interval = 13, r.fx.start = function() {
        bb || (bb = !0, eb());
    }, r.fx.stop = function() {
        bb = null;
    }, r.fx.speeds = {
        slow: 600,
        fast: 200,
        _default: 400
    }, r.fn.delay = function(b, c) {
        return b = r.fx ? r.fx.speeds[b] || b : b, c = c || 'fx', this.queue(c, function(c, d) {
            var e = a.setTimeout(c, b);
            d.stop = function() {
                a.clearTimeout(e);
            };
        });
    }, function() {
        var _$a = d.createElement('input');
        var _$b = d.createElement('select');
        var c = _$b.appendChild(d.createElement('option'));
        _$a.type = 'checkbox', o.checkOn = _$a.value !== '', o.optSelected = c.selected, _$a = d.createElement('input'), _$a.value = 't', _$a.type = 'radio', o.radioValue = _$a.value === 't';
    }();
    var lb;
    var mb = r.expr.attrHandle;
    r.fn.extend({
        attr: function attr(a, b) {
            return T(this, r.attr, a, b, arguments.length > 1);
        },
        removeAttr: function removeAttr(a) {
            return this.each(function() {
                r.removeAttr(this, a);
            });
        }
    }), r.extend({
        attr: function attr(a, b, c) {
            var d;
            var e;
            var f = a.nodeType;
            if (f !== 3 && f !== 8 && f !== 2) {
                return typeof a.getAttribute == 'undefined' ? r.prop(a, b, c) : (f === 1 && r.isXMLDoc(a) || (e = r.attrHooks[b.toLowerCase()] || (r.expr.match.bool.test(b) ? lb : void 0)), void 0 !== c ? c === null ? void r.removeAttr(a, b) : e && 'set' in e && void 0 !== (d = e.set(a, c, b)) ? d : (a.setAttribute(b, c + ''), c) : e && 'get' in e && (d = e.get(a, b)) !== null ? d : (d = r.find.attr(a, b), d == null ? void 0 : d));
            }
        },
        attrHooks: {
            type: {
                set: function set(a, b) {
                    if (!o.radioValue && b === 'radio' && B(a, 'input')) {
                        var c = a.value;
                        return a.setAttribute('type', b), c && (a.value = c), b;
                    }
                }
            }
        },
        removeAttr: function removeAttr(a, b) {
            var c;
            var d = 0;
            var e = b && b.match(L);
            if (e && a.nodeType === 1) while(c = e[d++])a.removeAttribute(c);
        }
    }), lb = {
        set: function set(a, b, c) {
            return b === !1 ? r.removeAttr(a, c) : a.setAttribute(c, c), c;
        }
    }, r.each(r.expr.match.bool.source.match(/\w+/g), function(a, b) {
        var c = mb[b] || r.find.attr;
        mb[b] = function(a, b, d) {
            var e;
            var f;
            var g = b.toLowerCase();
            return d || (f = mb[g], mb[g] = e, e = c(a, b, d) != null ? g : null, mb[g] = f), e;
        };
    });
    var nb = /^(?:input|select|textarea|button)$/i;
    var ob = /^(?:a|area)$/i;
    r.fn.extend({
        prop: function prop(a, b) {
            return T(this, r.prop, a, b, arguments.length > 1);
        },
        removeProp: function removeProp(a) {
            return this.each(function() {
                delete this[r.propFix[a] || a];
            });
        }
    }), r.extend({
        prop: function prop(a, b, c) {
            var d;
            var e;
            var f = a.nodeType;
            if (f !== 3 && f !== 8 && f !== 2) return f === 1 && r.isXMLDoc(a) || (b = r.propFix[b] || b, e = r.propHooks[b]), void 0 !== c ? e && 'set' in e && void 0 !== (d = e.set(a, c, b)) ? d : a[b] = c : e && 'get' in e && (d = e.get(a, b)) !== null ? d : a[b];
        },
        propHooks: {
            tabIndex: {
                get: function get(a) {
                    var _$b = r.find.attr(a, 'tabindex');
                    return _$b ? parseInt(_$b, 10) : nb.test(a.nodeName) || ob.test(a.nodeName) && a.href ? 0 : -1;
                }
            }
        },
        propFix: {
            for: 'htmlFor',
            class: 'className'
        }
    }), o.optSelected || (r.propHooks.selected = {
        get: function get(a) {
            var _$b = a.parentNode;
            return _$b && _$b.parentNode && _$b.parentNode.selectedIndex, null;
        },
        set: function set(a) {
            var _$b = a.parentNode;
            _$b && (_$b.selectedIndex, _$b.parentNode && _$b.parentNode.selectedIndex);
        }
    }), r.each([
        'tabIndex',
        'readOnly',
        'maxLength',
        'cellSpacing',
        'cellPadding',
        'rowSpan',
        'colSpan',
        'useMap',
        'frameBorder',
        'contentEditable'
    ], function() {
        r.propFix[this.toLowerCase()] = this;
    });
    function pb(a) {
        var _$b = a.match(L) || [];
        return _$b.join(' ');
    }
    function qb(a) {
        return a.getAttribute && a.getAttribute('class') || '';
    }
    r.fn.extend({
        addClass: function addClass(a) {
            var _$b;
            var c;
            var d;
            var e;
            var f;
            var g;
            var h;
            var i = 0;
            if (r.isFunction(a)) return this.each(function(b) {
                r(this).addClass(a.call(this, b, qb(this)));
            });
            if (typeof a == 'string' && a) {
                _$b = a.match(L) || [];
                while(c = this[i++])if (e = qb(c), d = c.nodeType === 1 && ' ' + pb(e) + ' ') {
                    g = 0;
                    while(f = _$b[g++])d.indexOf(' ' + f + ' ') < 0 && (d += f + ' ');
                    h = pb(d), e !== h && c.setAttribute('class', h);
                }
            }
            return this;
        },
        removeClass: function removeClass(a) {
            var _$b;
            var c;
            var d;
            var e;
            var f;
            var g;
            var h;
            var i = 0;
            if (r.isFunction(a)) return this.each(function(b) {
                r(this).removeClass(a.call(this, b, qb(this)));
            });
            if (!arguments.length) return this.attr('class', '');
            if (typeof a == 'string' && a) {
                _$b = a.match(L) || [];
                while(c = this[i++])if (e = qb(c), d = c.nodeType === 1 && ' ' + pb(e) + ' ') {
                    g = 0;
                    while(f = _$b[g++])while(d.indexOf(' ' + f + ' ') > -1)d = d.replace(' ' + f + ' ', ' ');
                    h = pb(d), e !== h && c.setAttribute('class', h);
                }
            }
            return this;
        },
        toggleClass: function toggleClass(a, b) {
            var c = typeof a === "undefined" ? "undefined" : _type_of(a);
            return typeof b == 'boolean' && c === 'string' ? b ? this.addClass(a) : this.removeClass(a) : r.isFunction(a) ? this.each(function(c) {
                r(this).toggleClass(a.call(this, c, qb(this), b), b);
            }) : this.each(function() {
                var _$b, d, e, f;
                if (c === 'string') {
                    d = 0, e = r(this), f = a.match(L) || [];
                    while(_$b = f[d++])e.hasClass(_$b) ? e.removeClass(_$b) : e.addClass(_$b);
                } else void 0 !== a && c !== 'boolean' || (_$b = qb(this), _$b && W.set(this, '__className__', _$b), this.setAttribute && this.setAttribute('class', _$b || a === !1 ? '' : W.get(this, '__className__') || ''));
            });
        },
        hasClass: function hasClass(a) {
            var _$b;
            var c;
            var d = 0;
            _$b = ' ' + a + ' ';
            while(c = this[d++])if (c.nodeType === 1 && (' ' + pb(qb(c)) + ' ').indexOf(_$b) > -1) return !0;
            return !1;
        }
    });
    var rb = /\r/g;
    r.fn.extend({
        val: function val(a) {
            var _$b;
            var c;
            var d;
            var e = this[0];
            {
                if (arguments.length) return d = r.isFunction(a), this.each(function(c) {
                    var e;
                    this.nodeType === 1 && (e = d ? a.call(this, c, r(this).val()) : a, e == null ? e = '' : typeof e == 'number' ? e += '' : Array.isArray(e) && (e = r.map(e, function(a) {
                        return a == null ? '' : a + '';
                    })), _$b = r.valHooks[this.type] || r.valHooks[this.nodeName.toLowerCase()], _$b && 'set' in _$b && void 0 !== _$b.set(this, e, 'value') || (this.value = e));
                });
                if (e) return _$b = r.valHooks[e.type] || r.valHooks[e.nodeName.toLowerCase()], _$b && 'get' in _$b && void 0 !== (c = _$b.get(e, 'value')) ? c : (c = e.value, typeof c == 'string' ? c.replace(rb, '') : c == null ? '' : c);
            }
        }
    }), r.extend({
        valHooks: {
            option: {
                get: function get(a) {
                    var _$b = r.find.attr(a, 'value');
                    return _$b != null ? _$b : pb(r.text(a));
                }
            },
            select: {
                get: function get(a) {
                    var _$b;
                    var c;
                    var d;
                    var e = a.options;
                    var f = a.selectedIndex;
                    var g = a.type === 'select-one';
                    var h = g ? null : [];
                    var i = g ? f + 1 : e.length;
                    for(d = f < 0 ? i : g ? f : 0; d < i; d++)if (c = e[d], (c.selected || d === f) && !c.disabled && (!c.parentNode.disabled || !B(c.parentNode, 'optgroup'))) {
                        if (_$b = r(c).val(), g) return _$b;
                        h.push(_$b);
                    }
                    return h;
                },
                set: function set(a, b) {
                    var c;
                    var d;
                    var e = a.options;
                    var f = r.makeArray(b);
                    var g = e.length;
                    while(g--)d = e[g], (d.selected = r.inArray(r.valHooks.option.get(d), f) > -1) && (c = !0);
                    return c || (a.selectedIndex = -1), f;
                }
            }
        }
    }), r.each([
        'radio',
        'checkbox'
    ], function() {
        r.valHooks[this] = {
            set: function set(a, b) {
                if (Array.isArray(b)) return a.checked = r.inArray(r(a).val(), b) > -1;
            }
        }, o.checkOn || (r.valHooks[this].get = function(a) {
            return a.getAttribute('value') === null ? 'on' : a.value;
        });
    });
    var sb = /^(?:focusinfocus|focusoutblur)$/;
    r.extend(r.event, {
        trigger: function trigger(b, c, e, f) {
            var g;
            var h;
            var i;
            var j;
            var k;
            var m;
            var n;
            var o = [
                e || d
            ];
            var p = l.call(b, 'type') ? b.type : b;
            var q = l.call(b, 'namespace') ? b.namespace.split('.') : [];
            if (h = i = e = e || d, e.nodeType !== 3 && e.nodeType !== 8 && !sb.test(p + r.event.triggered) && (p.indexOf('.') > -1 && (q = p.split('.'), p = q.shift(), q.sort()), k = p.indexOf(':') < 0 && 'on' + p, b = b[r.expando] ? b : new r.Event(p, (typeof b === "undefined" ? "undefined" : _type_of(b)) == 'object' && b), b.isTrigger = f ? 2 : 3, b.namespace = q.join('.'), b.rnamespace = b.namespace ? new RegExp('(^|\\.)' + q.join('\\.(?:.*\\.|)') + '(\\.|$)') : null, b.result = void 0, b.target || (b.target = e), c = c == null ? [
                b
            ] : r.makeArray(c, [
                b
            ]), n = r.event.special[p] || {}, f || !n.trigger || n.trigger.apply(e, c) !== !1)) {
                if (!f && !n.noBubble && !r.isWindow(e)) {
                    for(j = n.delegateType || p, sb.test(j + p) || (h = h.parentNode); h; h = h.parentNode)o.push(h), i = h;
                    i === (e.ownerDocument || d) && o.push(i.defaultView || i.parentWindow || a);
                }
                g = 0;
                while((h = o[g++]) && !b.isPropagationStopped())b.type = g > 1 ? j : n.bindType || p, m = (W.get(h, 'events') || {})[b.type] && W.get(h, 'handle'), m && m.apply(h, c), m = k && h[k], m && m.apply && U(h) && (b.result = m.apply(h, c), b.result === !1 && b.preventDefault());
                return b.type = p, f || b.isDefaultPrevented() || n._default && n._default.apply(o.pop(), c) !== !1 || !U(e) || k && r.isFunction(e[p]) && !r.isWindow(e) && (i = e[k], i && (e[k] = null), r.event.triggered = p, e[p](), r.event.triggered = void 0, i && (e[k] = i)), b.result;
            }
        },
        simulate: function simulate(a, b, c) {
            var d = r.extend(new r.Event(), c, {
                type: a,
                isSimulated: !0
            });
            r.event.trigger(d, null, b);
        }
    }), r.fn.extend({
        trigger: function trigger(a, b) {
            return this.each(function() {
                r.event.trigger(a, b, this);
            });
        },
        triggerHandler: function triggerHandler(a, b) {
            var c = this[0];
            if (c) return r.event.trigger(a, b, c, !0);
        }
    }), r.each('blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu'.split(' '), function(a, b) {
        r.fn[b] = function(a, c) {
            return arguments.length > 0 ? this.on(b, null, a, c) : this.trigger(b);
        };
    }), r.fn.extend({
        hover: function hover(a, b) {
            return this.mouseenter(a).mouseleave(b || a);
        }
    }), o.focusin = 'onfocusin' in a, o.focusin || r.each({
        focus: 'focusin',
        blur: 'focusout'
    }, function(a, b) {
        var c = function c(a) {
            r.event.simulate(b, a.target, r.event.fix(a));
        };
        r.event.special[b] = {
            setup: function setup() {
                var d = this.ownerDocument || this;
                var e = W.access(d, b);
                e || d.addEventListener(a, c, !0), W.access(d, b, (e || 0) + 1);
            },
            teardown: function teardown() {
                var d = this.ownerDocument || this;
                var e = W.access(d, b) - 1;
                e ? W.access(d, b, e) : (d.removeEventListener(a, c, !0), W.remove(d, b));
            }
        };
    });
    var tb = a.location;
    var ub = r.now();
    var vb = /\?/;
    r.parseXML = function(b) {
        var c;
        if (!b || typeof b != 'string') return null;
        try {
            c = new a.DOMParser().parseFromString(b, 'text/xml');
        } catch (d) {
            c = void 0;
        }
        return c && !c.getElementsByTagName('parsererror').length || r.error('Invalid XML: ' + b), c;
    };
    var wb = /\[\]$/;
    var xb = /\r?\n/g;
    var yb = /^(?:submit|button|image|reset|file)$/i;
    var zb = /^(?:input|select|textarea|keygen)/i;
    function Ab(a, b, c, d) {
        var e;
        if (Array.isArray(b)) r.each(b, function(b, e) {
            c || wb.test(a) ? d(a, e) : Ab(a + '[' + ((typeof e === "undefined" ? "undefined" : _type_of(e)) == 'object' && e != null ? b : '') + ']', e, c, d);
        });
        else if (c || r.type(b) !== 'object') d(a, b);
        else for(e in b)Ab(a + '[' + e + ']', b[e], c, d);
    }
    r.param = function(a, b) {
        var c;
        var d = [];
        var e = function e(a, b) {
            var c = r.isFunction(b) ? b() : b;
            d[d.length] = encodeURIComponent(a) + '=' + encodeURIComponent(c == null ? '' : c);
        };
        if (Array.isArray(a) || a.jquery && !r.isPlainObject(a)) r.each(a, function() {
            e(this.name, this.value);
        });
        else for(c in a)Ab(c, a[c], b, e);
        return d.join('&');
    }, r.fn.extend({
        serialize: function serialize() {
            return r.param(this.serializeArray());
        },
        serializeArray: function serializeArray() {
            return this.map(function() {
                var _$a = r.prop(this, 'elements');
                return _$a ? r.makeArray(_$a) : this;
            }).filter(function() {
                var _$a = this.type;
                return this.name && !r(this).is(':disabled') && zb.test(this.nodeName) && !yb.test(_$a) && (this.checked || !ja.test(_$a));
            }).map(function(a, b) {
                var c = r(this).val();
                return c == null ? null : Array.isArray(c) ? r.map(c, function(a) {
                    return {
                        name: b.name,
                        value: a.replace(xb, '\r\n')
                    };
                }) : {
                    name: b.name,
                    value: c.replace(xb, '\r\n')
                };
            }).get();
        }
    });
    var Bb = /%20/g;
    var Cb = /#.*$/;
    var Db = /([?&])_=[^&]*/;
    var Eb = /^(.*?):[ \t]*([^\r\n]*)$/gm;
    var Fb = /^(?:about|app|app-storage|.+-extension|file|res|widget):$/;
    var Gb = /^(?:GET|HEAD)$/;
    var Hb = /^\/\//;
    var Ib = {};
    var Jb = {};
    var Kb = '*/'.concat('*');
    var Lb = d.createElement('a');
    Lb.href = tb.href;
    function Mb(a) {
        return function(b, c) {
            typeof b != 'string' && (c = b, b = '*');
            var d;
            var e = 0;
            var f = b.toLowerCase().match(L) || [];
            if (r.isFunction(c)) while(d = f[e++])d[0] === '+' ? (d = d.slice(1) || '*', (a[d] = a[d] || []).unshift(c)) : (a[d] = a[d] || []).push(c);
        };
    }
    function Nb(a, b, c, d) {
        var e = {};
        var f = a === Jb;
        function g(h) {
            var i;
            return e[h] = !0, r.each(a[h] || [], function(a, h) {
                var j = h(b, c, d);
                return typeof j != 'string' || f || e[j] ? f ? !(i = j) : void 0 : (b.dataTypes.unshift(j), g(j), !1);
            }), i;
        }
        return g(b.dataTypes[0]) || !e['*'] && g('*');
    }
    function Ob(a, b) {
        var c;
        var d;
        var e = r.ajaxSettings.flatOptions || {};
        for(c in b)void 0 !== b[c] && ((e[c] ? a : d || (d = {}))[c] = b[c]);
        return d && r.extend(!0, a, d), a;
    }
    function Pb(a, b, c) {
        var d;
        var e;
        var f;
        var g;
        var h = a.contents;
        var i = a.dataTypes;
        while(i[0] === '*')i.shift(), void 0 === d && (d = a.mimeType || b.getResponseHeader('Content-Type'));
        if (d) {
            for(e in h)if (h[e] && h[e].test(d)) {
                i.unshift(e);
                break;
            }
        }
        if (i[0] in c) f = i[0];
        else {
            for(e in c){
                if (!i[0] || a.converters[e + ' ' + i[0]]) {
                    f = e;
                    break;
                }
                g || (g = e);
            }
            f = f || g;
        }
        if (f) return f !== i[0] && i.unshift(f), c[f];
    }
    function Qb(a, b, c, d) {
        var e;
        var f;
        var g;
        var h;
        var i;
        var j = {};
        var k = a.dataTypes.slice();
        if (k[1]) for(g in a.converters)j[g.toLowerCase()] = a.converters[g];
        f = k.shift();
        while(f)if (a.responseFields[f] && (c[a.responseFields[f]] = b), !i && d && a.dataFilter && (b = a.dataFilter(b, a.dataType)), i = f, f = k.shift()) {
            if (f === '*') f = i;
            else if (i !== '*' && i !== f) {
                if (g = j[i + ' ' + f] || j['* ' + f], !g) {
                    for(e in j)if (h = e.split(' '), h[1] === f && (g = j[i + ' ' + h[0]] || j['* ' + h[0]])) {
                        g === !0 ? g = j[e] : j[e] !== !0 && (f = h[0], k.unshift(h[1]));
                        break;
                    }
                }
                if (g !== !0) if (g && a.throws) b = g(b);
                else try {
                    b = g(b);
                } catch (l) {
                    return {
                        state: 'parsererror',
                        error: g ? l : 'No conversion from ' + i + ' to ' + f
                    };
                }
            }
        }
        return {
            state: 'success',
            data: b
        };
    }
    r.extend({
        active: 0,
        lastModified: {},
        etag: {},
        ajaxSettings: {
            url: tb.href,
            type: 'GET',
            isLocal: Fb.test(tb.protocol),
            global: !0,
            processData: !0,
            async: !0,
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            accepts: {
                '*': Kb,
                text: 'text/plain',
                html: 'text/html',
                xml: 'application/xml, text/xml',
                json: 'application/json, text/javascript'
            },
            contents: {
                xml: /\bxml\b/,
                html: /\bhtml/,
                json: /\bjson\b/
            },
            responseFields: {
                xml: 'responseXML',
                text: 'responseText',
                json: 'responseJSON'
            },
            converters: {
                '* text': String,
                'text html': !0,
                'text json': JSON.parse,
                'text xml': r.parseXML
            },
            flatOptions: {
                url: !0,
                context: !0
            }
        },
        ajaxSetup: function ajaxSetup(a, b) {
            return b ? Ob(Ob(a, r.ajaxSettings), b) : Ob(r.ajaxSettings, a);
        },
        ajaxPrefilter: Mb(Ib),
        ajaxTransport: Mb(Jb),
        ajax: function ajax(b, c) {
            (typeof b === "undefined" ? "undefined" : _type_of(b)) == 'object' && (c = b, b = void 0), c = c || {};
            var e;
            var f;
            var g;
            var h;
            var i;
            var j;
            var k;
            var l;
            var m;
            var n;
            var o = r.ajaxSetup({}, c);
            var p = o.context || o;
            var q = o.context && (p.nodeType || p.jquery) ? r(p) : r.event;
            var s = r.Deferred();
            var t = r.Callbacks('once memory');
            var u = o.statusCode || {};
            var v = {};
            var w = {};
            var x = 'canceled';
            var y = {
                readyState: 0,
                getResponseHeader: function getResponseHeader(a) {
                    var _$b;
                    if (k) {
                        if (!h) {
                            h = {};
                            while(_$b = Eb.exec(g))h[_$b[1].toLowerCase()] = _$b[2];
                        }
                        _$b = h[a.toLowerCase()];
                    }
                    return _$b == null ? null : _$b;
                },
                getAllResponseHeaders: function getAllResponseHeaders() {
                    return k ? g : null;
                },
                setRequestHeader: function setRequestHeader(a, b) {
                    return k == null && (a = w[a.toLowerCase()] = w[a.toLowerCase()] || a, v[a] = b), this;
                },
                overrideMimeType: function overrideMimeType(a) {
                    return k == null && (o.mimeType = a), this;
                },
                statusCode: function statusCode(a) {
                    var _$b;
                    if (a) if (k) y.always(a[y.status]);
                    else for(_$b in a)u[_$b] = [
                        u[_$b],
                        a[_$b]
                    ];
                    return this;
                },
                abort: function abort(a) {
                    var _$b = a || x;
                    return e && e.abort(_$b), A(0, _$b), this;
                }
            };
            if (s.promise(y), o.url = ((b || o.url || tb.href) + '').replace(Hb, tb.protocol + '//'), o.type = c.method || c.type || o.method || o.type, o.dataTypes = (o.dataType || '*').toLowerCase().match(L) || [
                ''
            ], o.crossDomain == null) {
                j = d.createElement('a');
                try {
                    j.href = o.url, j.href = j.href, o.crossDomain = Lb.protocol + '//' + Lb.host != j.protocol + '//' + j.host;
                } catch (z) {
                    o.crossDomain = !0;
                }
            }
            if (o.data && o.processData && typeof o.data != 'string' && (o.data = r.param(o.data, o.traditional)), Nb(Ib, o, c, y), k) return y;
            l = r.event && o.global, l && r.active++ === 0 && r.event.trigger('ajaxStart'), o.type = o.type.toUpperCase(), o.hasContent = !Gb.test(o.type), f = o.url.replace(Cb, ''), o.hasContent ? o.data && o.processData && (o.contentType || '').indexOf('application/x-www-form-urlencoded') === 0 && (o.data = o.data.replace(Bb, '+')) : (n = o.url.slice(f.length), o.data && (f += (vb.test(f) ? '&' : '?') + o.data, delete o.data), o.cache === !1 && (f = f.replace(Db, '$1'), n = (vb.test(f) ? '&' : '?') + '_=' + ub++ + n), o.url = f + n), o.ifModified && (r.lastModified[f] && y.setRequestHeader('If-Modified-Since', r.lastModified[f]), r.etag[f] && y.setRequestHeader('If-None-Match', r.etag[f])), (o.data && o.hasContent && o.contentType !== !1 || c.contentType) && y.setRequestHeader('Content-Type', o.contentType), y.setRequestHeader('Accept', o.dataTypes[0] && o.accepts[o.dataTypes[0]] ? o.accepts[o.dataTypes[0]] + (o.dataTypes[0] !== '*' ? ', ' + Kb + '; q=0.01' : '') : o.accepts['*']);
            for(m in o.headers)y.setRequestHeader(m, o.headers[m]);
            if (o.beforeSend && (o.beforeSend.call(p, y, o) === !1 || k)) return y.abort();
            if (x = 'abort', t.add(o.complete), y.done(o.success), y.fail(o.error), e = Nb(Jb, o, c, y)) {
                if (y.readyState = 1, l && q.trigger('ajaxSend', [
                    y,
                    o
                ]), k) return y;
                o.async && o.timeout > 0 && (i = a.setTimeout(function() {
                    y.abort('timeout');
                }, o.timeout));
                try {
                    k = !1, e.send(v, A);
                } catch (z) {
                    if (k) throw z;
                    A(-1, z);
                }
            } else A(-1, 'No Transport');
            function A(b, c, d, h) {
                var j;
                var m;
                var n;
                var v;
                var w;
                var x = c;
                k || (k = !0, i && a.clearTimeout(i), e = void 0, g = h || '', y.readyState = b > 0 ? 4 : 0, j = b >= 200 && b < 300 || b === 304, d && (v = Pb(o, y, d)), v = Qb(o, v, y, j), j ? (o.ifModified && (w = y.getResponseHeader('Last-Modified'), w && (r.lastModified[f] = w), w = y.getResponseHeader('etag'), w && (r.etag[f] = w)), b === 204 || o.type === 'HEAD' ? x = 'nocontent' : b === 304 ? x = 'notmodified' : (x = v.state, m = v.data, n = v.error, j = !n)) : (n = x, !b && x || (x = 'error', b < 0 && (b = 0))), y.status = b, y.statusText = (c || x) + '', j ? s.resolveWith(p, [
                    m,
                    x,
                    y
                ]) : s.rejectWith(p, [
                    y,
                    x,
                    n
                ]), y.statusCode(u), u = void 0, l && q.trigger(j ? 'ajaxSuccess' : 'ajaxError', [
                    y,
                    o,
                    j ? m : n
                ]), t.fireWith(p, [
                    y,
                    x
                ]), l && (q.trigger('ajaxComplete', [
                    y,
                    o
                ]), --r.active || r.event.trigger('ajaxStop')));
            }
            return y;
        },
        getJSON: function getJSON(a, b, c) {
            return r.get(a, b, c, 'json');
        },
        getScript: function getScript(a, b) {
            return r.get(a, void 0, b, 'script');
        }
    }), r.each([
        'get',
        'post'
    ], function(a, b) {
        r[b] = function(a, c, d, e) {
            return r.isFunction(c) && (e = e || d, d = c, c = void 0), r.ajax(r.extend({
                url: a,
                type: b,
                dataType: e,
                data: c,
                success: d
            }, r.isPlainObject(a) && a));
        };
    }), r._evalUrl = function(a) {
        return r.ajax({
            url: a,
            type: 'GET',
            dataType: 'script',
            cache: !0,
            async: !1,
            global: !1,
            throws: !0
        });
    }, r.fn.extend({
        wrapAll: function wrapAll(a) {
            var _$b;
            return this[0] && (r.isFunction(a) && (a = a.call(this[0])), _$b = r(a, this[0].ownerDocument).eq(0).clone(!0), this[0].parentNode && _$b.insertBefore(this[0]), _$b.map(function() {
                var _$a = this;
                while(_$a.firstElementChild)_$a = _$a.firstElementChild;
                return _$a;
            }).append(this)), this;
        },
        wrapInner: function wrapInner(a) {
            return r.isFunction(a) ? this.each(function(b) {
                r(this).wrapInner(a.call(this, b));
            }) : this.each(function() {
                var _$b = r(this);
                var c = _$b.contents();
                c.length ? c.wrapAll(a) : _$b.append(a);
            });
        },
        wrap: function wrap(a) {
            var _$b = r.isFunction(a);
            return this.each(function(c) {
                r(this).wrapAll(_$b ? a.call(this, c) : a);
            });
        },
        unwrap: function unwrap(a) {
            return this.parent(a).not('body').each(function() {
                r(this).replaceWith(this.childNodes);
            }), this;
        }
    }), r.expr.pseudos.hidden = function(a) {
        return !r.expr.pseudos.visible(a);
    }, r.expr.pseudos.visible = function(a) {
        return !!(a.offsetWidth || a.offsetHeight || a.getClientRects().length);
    }, r.ajaxSettings.xhr = function() {
        try {
            return new a.XMLHttpRequest();
        } catch (b) {}
    };
    var Rb = {
        0: 200,
        1223: 204
    };
    var Sb = r.ajaxSettings.xhr();
    o.cors = !!Sb && 'withCredentials' in Sb, o.ajax = Sb = !!Sb, r.ajaxTransport(function(b) {
        var c, d;
        if (o.cors || Sb && !b.crossDomain) return {
            send: function send(e, f) {
                var g;
                var h = b.xhr();
                if (h.open(b.type, b.url, b.async, b.username, b.password), b.xhrFields) for(g in b.xhrFields)h[g] = b.xhrFields[g];
                b.mimeType && h.overrideMimeType && h.overrideMimeType(b.mimeType), b.crossDomain || e['X-Requested-With'] || (e['X-Requested-With'] = 'XMLHttpRequest');
                for(g in e)h.setRequestHeader(g, e[g]);
                c = function(a) {
                    return function() {
                        c && (c = d = h.onload = h.onerror = h.onabort = h.onreadystatechange = null, a === 'abort' ? h.abort() : a === 'error' ? typeof h.status != 'number' ? f(0, 'error') : f(h.status, h.statusText) : f(Rb[h.status] || h.status, h.statusText, (h.responseType || 'text') !== 'text' || typeof h.responseText != 'string' ? {
                            binary: h.response
                        } : {
                            text: h.responseText
                        }, h.getAllResponseHeaders()));
                    };
                }, h.onload = c(), d = h.onerror = c('error'), void 0 !== h.onabort ? h.onabort = d : h.onreadystatechange = function() {
                    h.readyState === 4 && a.setTimeout(function() {
                        c && d();
                    });
                }, c = c('abort');
                try {
                    h.send(b.hasContent && b.data || null);
                } catch (i) {
                    if (c) throw i;
                }
            },
            abort: function abort() {
                c && c();
            }
        };
    }), r.ajaxPrefilter(function(a) {
        a.crossDomain && (a.contents.script = !1);
    }), r.ajaxSetup({
        accepts: {
            script: 'text/javascript, application/javascript, application/ecmascript, application/x-ecmascript'
        },
        contents: {
            script: /\b(?:java|ecma)script\b/
        },
        converters: {
            'text script': function(a) {
                return r.globalEval(a), a;
            }
        }
    }), r.ajaxPrefilter('script', function(a) {
        void 0 === a.cache && (a.cache = !1), a.crossDomain && (a.type = 'GET');
    }), r.ajaxTransport('script', function(a) {
        if (a.crossDomain) {
            var _$b, c;
            return {
                send: function send(e, f) {
                    _$b = r('<script>').prop({
                        charset: a.scriptCharset,
                        src: a.url
                    }).on('load error', c = function(a) {
                        _$b.remove(), c = null, a && f(a.type === 'error' ? 404 : 200, a.type);
                    }), d.head.appendChild(_$b[0]);
                },
                abort: function abort() {
                    c && c();
                }
            };
        }
    });
    var Tb = [];
    var Ub = /(=)\?(?=&|$)|\?\?/;
    r.ajaxSetup({
        jsonp: 'callback',
        jsonpCallback: function jsonpCallback() {
            var _$a = Tb.pop() || r.expando + '_' + ub++;
            return this[_$a] = !0, _$a;
        }
    }), r.ajaxPrefilter('json jsonp', function(b, c, d) {
        var e;
        var f;
        var g;
        var h = b.jsonp !== !1 && (Ub.test(b.url) ? 'url' : typeof b.data == 'string' && (b.contentType || '').indexOf('application/x-www-form-urlencoded') === 0 && Ub.test(b.data) && 'data');
        if (h || b.dataTypes[0] === 'jsonp') return e = b.jsonpCallback = r.isFunction(b.jsonpCallback) ? b.jsonpCallback() : b.jsonpCallback, h ? b[h] = b[h].replace(Ub, '$1' + e) : b.jsonp !== !1 && (b.url += (vb.test(b.url) ? '&' : '?') + b.jsonp + '=' + e), b.converters['script json'] = function() {
            return g || r.error(e + ' was not called'), g[0];
        }, b.dataTypes[0] = 'json', f = a[e], a[e] = function() {
            g = arguments;
        }, d.always(function() {
            void 0 === f ? r(a).removeProp(e) : a[e] = f, b[e] && (b.jsonpCallback = c.jsonpCallback, Tb.push(e)), g && r.isFunction(f) && f(g[0]), g = f = void 0;
        }), 'script';
    }), o.createHTMLDocument = function() {
        var _$a = d.implementation.createHTMLDocument('').body;
        return _$a.innerHTML = '<form></form><form></form>', _$a.childNodes.length === 2;
    }(), r.parseHTML = function(a, b, c) {
        if (typeof a != 'string') return [];
        typeof b == 'boolean' && (c = b, b = !1);
        var e, f, g;
        return b || (o.createHTMLDocument ? (b = d.implementation.createHTMLDocument(''), e = b.createElement('base'), e.href = d.location.href, b.head.appendChild(e)) : b = d), f = C.exec(a), g = !c && [], f ? [
            b.createElement(f[1])
        ] : (f = qa([
            a
        ], b, g), g && g.length && r(g).remove(), r.merge([], f.childNodes));
    }, r.fn.load = function(a, b, c) {
        var d;
        var e;
        var f;
        var g = this;
        var h = a.indexOf(' ');
        return h > -1 && (d = pb(a.slice(h)), a = a.slice(0, h)), r.isFunction(b) ? (c = b, b = void 0) : b && (typeof b === "undefined" ? "undefined" : _type_of(b)) == 'object' && (e = 'POST'), g.length > 0 && r.ajax({
            url: a,
            type: e || 'GET',
            dataType: 'html',
            data: b
        }).done(function(a) {
            f = arguments, g.html(d ? r('<div>').append(r.parseHTML(a)).find(d) : a);
        }).always(c && function(a, b) {
            g.each(function() {
                c.apply(this, f || [
                    a.responseText,
                    b,
                    a
                ]);
            });
        }), this;
    }, r.each([
        'ajaxStart',
        'ajaxStop',
        'ajaxComplete',
        'ajaxError',
        'ajaxSuccess',
        'ajaxSend'
    ], function(a, b) {
        r.fn[b] = function(a) {
            return this.on(b, a);
        };
    }), r.expr.pseudos.animated = function(a) {
        return r.grep(r.timers, function(b) {
            return a === b.elem;
        }).length;
    }, r.offset = {
        setOffset: function setOffset(a, b, c) {
            var d;
            var e;
            var f;
            var g;
            var h;
            var i;
            var j;
            var k = r.css(a, 'position');
            var l = r(a);
            var m = {};
            k === 'static' && (a.style.position = 'relative'), h = l.offset(), f = r.css(a, 'top'), i = r.css(a, 'left'), j = (k === 'absolute' || k === 'fixed') && (f + i).indexOf('auto') > -1, j ? (d = l.position(), g = d.top, e = d.left) : (g = parseFloat(f) || 0, e = parseFloat(i) || 0), r.isFunction(b) && (b = b.call(a, c, r.extend({}, h))), b.top != null && (m.top = b.top - h.top + g), b.left != null && (m.left = b.left - h.left + e), 'using' in b ? b.using.call(a, m) : l.css(m);
        }
    }, r.fn.extend({
        offset: function offset(a) {
            if (arguments.length) return void 0 === a ? this : this.each(function(b) {
                r.offset.setOffset(this, a, b);
            });
            var _$b;
            var c;
            var d;
            var e;
            var f = this[0];
            if (f) return f.getClientRects().length ? (d = f.getBoundingClientRect(), _$b = f.ownerDocument, c = _$b.documentElement, e = _$b.defaultView, {
                top: d.top + e.pageYOffset - c.clientTop,
                left: d.left + e.pageXOffset - c.clientLeft
            }) : {
                top: 0,
                left: 0
            };
        },
        position: function position() {
            if (this[0]) {
                var _$a;
                var _$b;
                var c = this[0];
                var d = {
                    top: 0,
                    left: 0
                };
                return r.css(c, 'position') === 'fixed' ? _$b = c.getBoundingClientRect() : (_$a = this.offsetParent(), _$b = this.offset(), B(_$a[0], 'html') || (d = _$a.offset()), d = {
                    top: d.top + r.css(_$a[0], 'borderTopWidth', !0),
                    left: d.left + r.css(_$a[0], 'borderLeftWidth', !0)
                }), {
                    top: _$b.top - d.top - r.css(c, 'marginTop', !0),
                    left: _$b.left - d.left - r.css(c, 'marginLeft', !0)
                };
            }
        },
        offsetParent: function offsetParent() {
            return this.map(function() {
                var _$a = this.offsetParent;
                while(_$a && r.css(_$a, 'position') === 'static')_$a = _$a.offsetParent;
                return _$a || ra;
            });
        }
    }), r.each({
        scrollLeft: 'pageXOffset',
        scrollTop: 'pageYOffset'
    }, function(a, b) {
        var c = b === 'pageYOffset';
        r.fn[a] = function(d) {
            return T(this, function(a, d, e) {
                var f;
                return r.isWindow(a) ? f = a : a.nodeType === 9 && (f = a.defaultView), void 0 === e ? f ? f[b] : a[d] : void (f ? f.scrollTo(c ? f.pageXOffset : e, c ? e : f.pageYOffset) : a[d] = e);
            }, a, d, arguments.length);
        };
    }), r.each([
        'top',
        'left'
    ], function(a, b) {
        r.cssHooks[b] = Pa(o.pixelPosition, function(a, c) {
            if (c) return c = Oa(a, b), Ma.test(c) ? r(a).position()[b] + 'px' : c;
        });
    }), r.each({
        Height: 'height',
        Width: 'width'
    }, function(a, b) {
        r.each({
            padding: 'inner' + a,
            content: b,
            '': 'outer' + a
        }, function(c, d) {
            r.fn[d] = function(e, f) {
                var g = arguments.length && (c || typeof e != 'boolean');
                var h = c || (e === !0 || f === !0 ? 'margin' : 'border');
                return T(this, function(b, c, e) {
                    var _$f;
                    return r.isWindow(b) ? d.indexOf('outer') === 0 ? b['inner' + a] : b.document.documentElement['client' + a] : b.nodeType === 9 ? (_$f = b.documentElement, Math.max(b.body['scroll' + a], _$f['scroll' + a], b.body['offset' + a], _$f['offset' + a], _$f['client' + a])) : void 0 === e ? r.css(b, c, h) : r.style(b, c, e, h);
                }, b, g ? e : void 0, g);
            };
        });
    }), r.fn.extend({
        bind: function bind(a, b, c) {
            return this.on(a, null, b, c);
        },
        unbind: function unbind(a, b) {
            return this.off(a, null, b);
        },
        delegate: function delegate(a, b, c, d) {
            return this.on(b, a, c, d);
        },
        undelegate: function undelegate(a, b, c) {
            return arguments.length === 1 ? this.off(a, '**') : this.off(b, a || '**', c);
        }
    }), r.holdReady = function(a) {
        a ? r.readyWait++ : r.ready(!0);
    }, r.isArray = Array.isArray, r.parseJSON = JSON.parse, r.nodeName = B, typeof define == 'function' && define.amd && define('jquery', [], function() {
        return r;
    });
    var Vb = a.jQuery;
    var Wb = a.$;
    return r.noConflict = function(b) {
        return a.$ === r && (a.$ = Wb), b && a.jQuery === r && (a.jQuery = Vb), r;
    }, b || (a.jQuery = a.$ = r), r;
});

// IE DETECTION	-- DETECTING WITH HTML, MAY NOT WORK PROPERLY
/* var ie = (function(){

    var undef,
        v = 3,
        div = document.createElement('div'),
        all = div.getElementsByTagName('i');

    while (
        div.innerHTML = '<!--[if gt IE ' + (++v) + ']><i></i><![endif]-->',
        all[0]
    );

    return v > 4 ? v : undef;

}()); */ // IE DETECTION	-- DETECTING WITH USER AGENT
function getInternetExplorerVersion() // Returns the version of Internet Explorer or a -1
// (indicating the use of another browser).
{
    var rv = -1; // Return value assumes failure.
    if (navigator.appName == 'Microsoft Internet Explorer') {
        var ua = navigator.userAgent;
        var re = new RegExp('MSIE ([0-9]{1,}[\.0-9]{0,})');
        if (re.exec(ua) != null) {
            rv = parseFloat(RegExp.$1);
        }
    }
    return rv;
}
var ie = getInternetExplorerVersion();
function getQueryVariable(variable) {
    var query = window.location.search.substring(1); // search in parent
    var vars = query.split('&');
    for(var i = 0; i < vars.length; i++){
        var pair = vars[i].match(/([^=]+?)=(.+)/);
        if (pair && decodeURIComponent(pair[1]) == variable) {
            return decodeURIComponent(pair[2]);
        }
    }
} // var nohtml5_message = {
 //	'en':'This game is best experienced with HTML5 capable browsers. We recommend downloading <a href="http://www.google.com/chrome" target="_blank">Google Chrome</a>, or <a href="http://www.mozilla.org" target="_blank">Mozilla Firefox</a>',
 //	'hk':'此程式只能支援 HTML5 的瀏覽器上運行，如未能開始遊戲，請按此下載 <a href="http://www.google.com/chrome" target="_blank">Google Chrome</a>, or <a href="http://www.mozilla.org" target="_blank">Mozilla Firefox</a>',
 //	'tw':'此程式只能支援 HTML5 的瀏覽器上運行，如未能開始遊戲，請按此下載 <a href="http://www.google.com/chrome" target="_blank">Google Chrome</a>, or <a href="http://www.mozilla.org" target="_blank">Mozilla Firefox</a>',
 // }
 //
 // if(ie<9){
 //	var message;
 //	var lang = getQueryVariable('lang');
 //	if(lang){
 //		switch(lang.toLowerCase()){
 //			case 'en':
 //				message = nohtml5_message['en'];
 //				break;
 //			case 'hk':
 //				message = nohtml5_message['hk'];
 //				break;
 //			case 'tw':
 //				message = nohtml5_message['tw'];
 //				break;
 //			default:
 //				message = nohtml5_message['en'];
 //		}
 //	}else{
 //		message = nohtml5_message['en'];
 //	}
 //
 //	// INJECT
 //	document.getElementById('nohtml5-text').innerHTML = message;
 //
 //	// SHOW
 //	document.getElementById('nohtml5').style.visibility="visible";
 //
 // }

function _instanceof(left, right) {
    if (right != null && typeof Symbol !== "undefined" && right[Symbol.hasInstance]) {
        return !!right[Symbol.hasInstance](left);
    } else {
        return left instanceof right;
    }
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
/*!
 *  howler.js v2.2.3
 *  howlerjs.com
 *
 *  (c) 2013-2020, James Simpson of GoldFire Studios
 *  goldfirestudios.com
 *
 *  MIT License
 */ (function() {
    'use strict';
    /** Global Methods **/ /***************************************************************************/ /**
   * Create the global controller. All contained methods and properties apply
   * to all sounds that are currently playing or will be in the future.
   */ var HowlerGlobal1 = function HowlerGlobal1() {
        this.init();
    };
    HowlerGlobal1.prototype = {
        /**
     * Initialize the global Howler object.
     * @return {Howler}
     */ init: function init() {
            var self = this || Howler1;
            // Create a global ID counter.
            self._counter = 1000;
            // Pool of unlocked HTML5 Audio objects.
            self._html5AudioPool = [];
            self.html5PoolSize = 10;
            // Internal properties.
            self._codecs = {};
            self._howls = [];
            self._muted = false;
            self._volume = 1;
            self._canPlayEvent = 'canplaythrough';
            self._navigator = typeof window !== 'undefined' && window.navigator ? window.navigator : null;
            // Public properties.
            self.masterGain = null;
            self.noAudio = false;
            self.usingWebAudio = true;
            self.autoSuspend = true;
            self.ctx = null;
            // Set to false to disable the auto audio unlocker.
            self.autoUnlock = true;
            // Setup the various state values for global tracking.
            self._setup();
            return self;
        },
        /**
     * Get/set the global volume for all sounds.
     * @param  {Float} vol Volume from 0.0 to 1.0.
     * @return {Howler/Float}     Returns self or current volume.
     */ volume: function volume(vol) {
            var self = this || Howler1;
            vol = parseFloat(vol);
            // If we don't have an AudioContext created yet, run the setup.
            if (!self.ctx) {
                setupAudioContext();
            }
            if (typeof vol !== 'undefined' && vol >= 0 && vol <= 1) {
                self._volume = vol;
                // Don't update any of the nodes if we are muted.
                if (self._muted) {
                    return self;
                }
                // When using Web Audio, we just need to adjust the master gain.
                if (self.usingWebAudio) {
                    self.masterGain.gain.setValueAtTime(vol, Howler1.ctx.currentTime);
                }
                // Loop through and change volume for all HTML5 audio nodes.
                for(var i = 0; i < self._howls.length; i++){
                    if (!self._howls[i]._webAudio) {
                        // Get all of the sounds in this Howl group.
                        var ids = self._howls[i]._getSoundIds();
                        // Loop through all sounds and change the volumes.
                        for(var j = 0; j < ids.length; j++){
                            var sound = self._howls[i]._soundById(ids[j]);
                            if (sound && sound._node) {
                                sound._node.volume = sound._volume * vol;
                            }
                        }
                    }
                }
                return self;
            }
            return self._volume;
        },
        /**
     * Handle muting and unmuting globally.
     * @param  {Boolean} muted Is muted or not.
     */ mute: function mute(muted) {
            var self = this || Howler1;
            // If we don't have an AudioContext created yet, run the setup.
            if (!self.ctx) {
                setupAudioContext();
            }
            self._muted = muted;
            // With Web Audio, we just need to mute the master gain.
            if (self.usingWebAudio) {
                self.masterGain.gain.setValueAtTime(muted ? 0 : self._volume, Howler1.ctx.currentTime);
            }
            // Loop through and mute all HTML5 Audio nodes.
            for(var i = 0; i < self._howls.length; i++){
                if (!self._howls[i]._webAudio) {
                    // Get all of the sounds in this Howl group.
                    var ids = self._howls[i]._getSoundIds();
                    // Loop through all sounds and mark the audio node as muted.
                    for(var j = 0; j < ids.length; j++){
                        var sound = self._howls[i]._soundById(ids[j]);
                        if (sound && sound._node) {
                            sound._node.muted = muted ? true : sound._muted;
                        }
                    }
                }
            }
            return self;
        },
        /**
     * Handle stopping all sounds globally.
     */ stop: function stop() {
            var self = this || Howler1;
            // Loop through all Howls and stop them.
            for(var i = 0; i < self._howls.length; i++){
                self._howls[i].stop();
            }
            return self;
        },
        /**
     * Unload and destroy all currently loaded Howl objects.
     * @return {Howler}
     */ unload: function unload() {
            var self = this || Howler1;
            for(var i = self._howls.length - 1; i >= 0; i--){
                self._howls[i].unload();
            }
            // Create a new AudioContext to make sure it is fully reset.
            if (self.usingWebAudio && self.ctx && typeof self.ctx.close !== 'undefined') {
                self.ctx.close();
                self.ctx = null;
                setupAudioContext();
            }
            return self;
        },
        /**
     * Check for codec support of specific extension.
     * @param  {String} ext Audio file extention.
     * @return {Boolean}
     */ codecs: function codecs(ext) {
            return (this || Howler1)._codecs[ext.replace(/^x-/, '')];
        },
        /**
     * Setup various state values for global tracking.
     * @return {Howler}
     */ _setup: function _setup() {
            var self = this || Howler1;
            // Keeps track of the suspend/resume state of the AudioContext.
            self.state = self.ctx ? self.ctx.state || 'suspended' : 'suspended';
            // Automatically begin the 30-second suspend process
            self._autoSuspend();
            // Check if audio is available.
            if (!self.usingWebAudio) {
                // No audio is available on this system if noAudio is set to true.
                if (typeof Audio !== 'undefined') {
                    try {
                        var test = new Audio();
                        // Check if the canplaythrough event is available.
                        if (typeof test.oncanplaythrough === 'undefined') {
                            self._canPlayEvent = 'canplay';
                        }
                    } catch (e) {
                        self.noAudio = true;
                    }
                } else {
                    self.noAudio = true;
                }
            }
            // Test to make sure audio isn't disabled in Internet Explorer.
            try {
                var test = new Audio();
                if (test.muted) {
                    self.noAudio = true;
                }
            } catch (e) {}
            // Check for supported codecs.
            if (!self.noAudio) {
                self._setupCodecs();
            }
            return self;
        },
        /**
     * Check for browser support for various codecs and cache the results.
     * @return {Howler}
     */ _setupCodecs: function _setupCodecs() {
            var self = this || Howler1;
            var audioTest = null;
            // Must wrap in a try/catch because IE11 in server mode throws an error.
            try {
                audioTest = typeof Audio !== 'undefined' ? new Audio() : null;
            } catch (err) {
                return self;
            }
            if (!audioTest || typeof audioTest.canPlayType !== 'function') {
                return self;
            }
            var mpegTest = audioTest.canPlayType('audio/mpeg;').replace(/^no$/, '');
            // Opera version <33 has mixed MP3 support, so we need to check for and block it.
            var ua = self._navigator ? self._navigator.userAgent : '';
            var checkOpera = ua.match(/OPR\/([0-6].)/g);
            var isOldOpera = checkOpera && parseInt(checkOpera[0].split('/')[1], 10) < 33;
            var checkSafari = ua.indexOf('Safari') !== -1 && ua.indexOf('Chrome') === -1;
            var safariVersion = ua.match(/Version\/(.*?) /);
            var isOldSafari = checkSafari && safariVersion && parseInt(safariVersion[1], 10) < 15;
            self._codecs = {
                mp3: !!(!isOldOpera && (mpegTest || audioTest.canPlayType('audio/mp3;').replace(/^no$/, ''))),
                mpeg: !!mpegTest,
                opus: !!audioTest.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/, ''),
                ogg: !!audioTest.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''),
                oga: !!audioTest.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''),
                wav: !!(audioTest.canPlayType('audio/wav; codecs="1"') || audioTest.canPlayType('audio/wav')).replace(/^no$/, ''),
                aac: !!audioTest.canPlayType('audio/aac;').replace(/^no$/, ''),
                caf: !!audioTest.canPlayType('audio/x-caf;').replace(/^no$/, ''),
                m4a: !!(audioTest.canPlayType('audio/x-m4a;') || audioTest.canPlayType('audio/m4a;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),
                m4b: !!(audioTest.canPlayType('audio/x-m4b;') || audioTest.canPlayType('audio/m4b;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),
                mp4: !!(audioTest.canPlayType('audio/x-mp4;') || audioTest.canPlayType('audio/mp4;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),
                weba: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/, '')),
                webm: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/, '')),
                dolby: !!audioTest.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/, ''),
                flac: !!(audioTest.canPlayType('audio/x-flac;') || audioTest.canPlayType('audio/flac;')).replace(/^no$/, '')
            };
            return self;
        },
        /**
     * Some browsers/devices will only allow audio to be played after a user interaction.
     * Attempt to automatically unlock audio on the first user interaction.
     * Concept from: http://paulbakaus.com/tutorials/html5/web-audio-on-ios/
     * @return {Howler}
     */ _unlockAudio: function _unlockAudio() {
            var self = this || Howler1;
            // Only run this if Web Audio is supported and it hasn't already been unlocked.
            if (self._audioUnlocked || !self.ctx) {
                return;
            }
            self._audioUnlocked = false;
            self.autoUnlock = false;
            // Some mobile devices/platforms have distortion issues when opening/closing tabs and/or web views.
            // Bugs in the browser (especially Mobile Safari) can cause the sampleRate to change from 44100 to 48000.
            // By calling Howler.unload(), we create a new AudioContext with the correct sampleRate.
            if (!self._mobileUnloaded && self.ctx.sampleRate !== 44100) {
                self._mobileUnloaded = true;
                self.unload();
            }
            // Scratch buffer for enabling iOS to dispose of web audio buffers correctly, as per:
            // http://stackoverflow.com/questions/24119684
            self._scratchBuffer = self.ctx.createBuffer(1, 1, 22050);
            // Call this method on touch start to create and play a buffer,
            // then check if the audio actually played to determine if
            // audio has now been unlocked on iOS, Android, etc.
            var unlock = function unlock1(e) {
                // Create a pool of unlocked HTML5 Audio objects that can
                // be used for playing sounds without user interaction. HTML5
                // Audio objects must be individually unlocked, as opposed
                // to the WebAudio API which only needs a single activation.
                // This must occur before WebAudio setup or the source.onended
                // event will not fire.
                while(self._html5AudioPool.length < self.html5PoolSize){
                    try {
                        var audioNode = new Audio();
                        // Mark this Audio object as unlocked to ensure it can get returned
                        // to the unlocked pool when released.
                        audioNode._unlocked = true;
                        // Add the audio node to the pool.
                        self._releaseHtml5Audio(audioNode);
                    } catch (e) {
                        self.noAudio = true;
                        break;
                    }
                }
                // Loop through any assigned audio nodes and unlock them.
                for(var i = 0; i < self._howls.length; i++){
                    if (!self._howls[i]._webAudio) {
                        // Get all of the sounds in this Howl group.
                        var ids = self._howls[i]._getSoundIds();
                        // Loop through all sounds and unlock the audio nodes.
                        for(var j = 0; j < ids.length; j++){
                            var sound = self._howls[i]._soundById(ids[j]);
                            if (sound && sound._node && !sound._node._unlocked) {
                                sound._node._unlocked = true;
                                sound._node.load();
                            }
                        }
                    }
                }
                // Fix Android can not play in suspend state.
                self._autoResume();
                // Create an empty buffer.
                var source = self.ctx.createBufferSource();
                source.buffer = self._scratchBuffer;
                source.connect(self.ctx.destination);
                // Play the empty buffer.
                if (typeof source.start === 'undefined') {
                    source.noteOn(0);
                } else {
                    source.start(0);
                }
                // Calling resume() on a stack initiated by user gesture is what actually unlocks the audio on Android Chrome >= 55.
                if (typeof self.ctx.resume === 'function') {
                    self.ctx.resume();
                }
                // Setup a timeout to check that we are unlocked on the next event loop.
                source.onended = function() {
                    source.disconnect(0);
                    // Update the unlocked state and prevent this check from happening again.
                    self._audioUnlocked = true;
                    // Remove the touch start listener.
                    document.removeEventListener('touchstart', unlock, true);
                    document.removeEventListener('touchend', unlock, true);
                    document.removeEventListener('click', unlock, true);
                    document.removeEventListener('keydown', unlock, true);
                    // Let all sounds know that audio has been unlocked.
                    for(var i = 0; i < self._howls.length; i++){
                        self._howls[i]._emit('unlock');
                    }
                };
            };
            // Setup a touch start listener to attempt an unlock in.
            document.addEventListener('touchstart', unlock, true);
            document.addEventListener('touchend', unlock, true);
            document.addEventListener('click', unlock, true);
            document.addEventListener('keydown', unlock, true);
            return self;
        },
        /**
     * Get an unlocked HTML5 Audio object from the pool. If none are left,
     * return a new Audio object and throw a warning.
     * @return {Audio} HTML5 Audio object.
     */ _obtainHtml5Audio: function _obtainHtml5Audio() {
            var self = this || Howler1;
            // Return the next object from the pool if one exists.
            if (self._html5AudioPool.length) {
                return self._html5AudioPool.pop();
            }
            // .Check if the audio is locked and throw a warning.
            var testPlay = new Audio().play();
            if (testPlay && typeof Promise !== 'undefined' && (_instanceof(testPlay, Promise) || typeof testPlay.then === 'function')) {
                testPlay.catch(function() {
                    console.warn('HTML5 Audio pool exhausted, returning potentially locked audio object.');
                });
            }
            return new Audio();
        },
        /**
     * Return an activated HTML5 Audio object to the pool.
     * @return {Howler}
     */ _releaseHtml5Audio: function _releaseHtml5Audio(audio) {
            var self = this || Howler1;
            // Don't add audio to the pool if we don't know if it has been unlocked.
            if (audio._unlocked) {
                self._html5AudioPool.push(audio);
            }
            return self;
        },
        /**
     * Automatically suspend the Web Audio AudioContext after no sound has played for 30 seconds.
     * This saves processing/energy and fixes various browser-specific bugs with audio getting stuck.
     * @return {Howler}
     */ _autoSuspend: function _autoSuspend() {
            var self = this;
            if (!self.autoSuspend || !self.ctx || typeof self.ctx.suspend === 'undefined' || !Howler1.usingWebAudio) {
                return;
            }
            // Check if any sounds are playing.
            for(var i = 0; i < self._howls.length; i++){
                if (self._howls[i]._webAudio) {
                    for(var j = 0; j < self._howls[i]._sounds.length; j++){
                        if (!self._howls[i]._sounds[j]._paused) {
                            return self;
                        }
                    }
                }
            }
            if (self._suspendTimer) {
                clearTimeout(self._suspendTimer);
            }
            // If no sound has played after 30 seconds, suspend the context.
            self._suspendTimer = setTimeout(function() {
                if (!self.autoSuspend) {
                    return;
                }
                self._suspendTimer = null;
                self.state = 'suspending';
                // Handle updating the state of the audio context after suspending.
                var handleSuspension = function handleSuspension() {
                    self.state = 'suspended';
                    if (self._resumeAfterSuspend) {
                        delete self._resumeAfterSuspend;
                        self._autoResume();
                    }
                };
                // Either the state gets suspended or it is interrupted.
                // Either way, we need to update the state to suspended.
                self.ctx.suspend().then(handleSuspension, handleSuspension);
            }, 30000);
            return self;
        },
        /**
     * Automatically resume the Web Audio AudioContext when a new sound is played.
     * @return {Howler}
     */ _autoResume: function _autoResume() {
            var self = this;
            if (!self.ctx || typeof self.ctx.resume === 'undefined' || !Howler1.usingWebAudio) {
                return;
            }
            if (self.state === 'running' && self.ctx.state !== 'interrupted' && self._suspendTimer) {
                clearTimeout(self._suspendTimer);
                self._suspendTimer = null;
            } else if (self.state === 'suspended' || self.state === 'running' && self.ctx.state === 'interrupted') {
                self.ctx.resume().then(function() {
                    self.state = 'running';
                    // Emit to all Howls that the audio has resumed.
                    for(var i = 0; i < self._howls.length; i++){
                        self._howls[i]._emit('resume');
                    }
                });
                if (self._suspendTimer) {
                    clearTimeout(self._suspendTimer);
                    self._suspendTimer = null;
                }
            } else if (self.state === 'suspending') {
                self._resumeAfterSuspend = true;
            }
            return self;
        }
    };
    // Setup the global audio controller.
    var Howler1 = new HowlerGlobal1();
    /** Group Methods **/ /***************************************************************************/ /**
   * Create an audio group controller.
   * @param {Object} o Passed in properties for this group.
   */ var Howl1 = function Howl1(o) {
        var self = this;
        // Throw an error if no source is provided.
        if (!o.src || o.src.length === 0) {
            console.error('An array of source files must be passed with any new Howl.');
            return;
        }
        self.init(o);
    };
    Howl1.prototype = {
        /**
     * Initialize a new Howl group object.
     * @param  {Object} o Passed in properties for this group.
     * @return {Howl}
     */ init: function init(o) {
            var self = this;
            // If we don't have an AudioContext created yet, run the setup.
            if (!Howler1.ctx) {
                setupAudioContext();
            }
            // Setup user-defined default properties.
            self._autoplay = o.autoplay || false;
            self._format = typeof o.format !== 'string' ? o.format : [
                o.format
            ];
            self._html5 = o.html5 || false;
            self._muted = o.mute || false;
            self._loop = o.loop || false;
            self._pool = o.pool || 5;
            self._preload = typeof o.preload === 'boolean' || o.preload === 'metadata' ? o.preload : true;
            self._rate = o.rate || 1;
            self._sprite = o.sprite || {};
            self._src = typeof o.src !== 'string' ? o.src : [
                o.src
            ];
            self._volume = o.volume !== undefined ? o.volume : 1;
            self._xhr = {
                method: o.xhr && o.xhr.method ? o.xhr.method : 'GET',
                headers: o.xhr && o.xhr.headers ? o.xhr.headers : null,
                withCredentials: o.xhr && o.xhr.withCredentials ? o.xhr.withCredentials : false
            };
            // Setup all other default properties.
            self._duration = 0;
            self._state = 'unloaded';
            self._sounds = [];
            self._endTimers = {};
            self._queue = [];
            self._playLock = false;
            // Setup event listeners.
            self._onend = o.onend ? [
                {
                    fn: o.onend
                }
            ] : [];
            self._onfade = o.onfade ? [
                {
                    fn: o.onfade
                }
            ] : [];
            self._onload = o.onload ? [
                {
                    fn: o.onload
                }
            ] : [];
            self._onloaderror = o.onloaderror ? [
                {
                    fn: o.onloaderror
                }
            ] : [];
            self._onplayerror = o.onplayerror ? [
                {
                    fn: o.onplayerror
                }
            ] : [];
            self._onpause = o.onpause ? [
                {
                    fn: o.onpause
                }
            ] : [];
            self._onplay = o.onplay ? [
                {
                    fn: o.onplay
                }
            ] : [];
            self._onstop = o.onstop ? [
                {
                    fn: o.onstop
                }
            ] : [];
            self._onmute = o.onmute ? [
                {
                    fn: o.onmute
                }
            ] : [];
            self._onvolume = o.onvolume ? [
                {
                    fn: o.onvolume
                }
            ] : [];
            self._onrate = o.onrate ? [
                {
                    fn: o.onrate
                }
            ] : [];
            self._onseek = o.onseek ? [
                {
                    fn: o.onseek
                }
            ] : [];
            self._onunlock = o.onunlock ? [
                {
                    fn: o.onunlock
                }
            ] : [];
            self._onresume = [];
            // Web Audio or HTML5 Audio?
            self._webAudio = Howler1.usingWebAudio && !self._html5;
            // Automatically try to enable audio.
            if (typeof Howler1.ctx !== 'undefined' && Howler1.ctx && Howler1.autoUnlock) {
                Howler1._unlockAudio();
            }
            // Keep track of this Howl group in the global controller.
            Howler1._howls.push(self);
            // If they selected autoplay, add a play event to the load queue.
            if (self._autoplay) {
                self._queue.push({
                    event: 'play',
                    action: function action() {
                        self.play();
                    }
                });
            }
            // Load the source file unless otherwise specified.
            if (self._preload && self._preload !== 'none') {
                self.load();
            }
            return self;
        },
        /**
     * Load the audio file.
     * @return {Howler}
     */ load: function load() {
            var self = this;
            var url = null;
            // If no audio is available, quit immediately.
            if (Howler1.noAudio) {
                self._emit('loaderror', null, 'No audio support.');
                return;
            }
            // Make sure our source is in an array.
            if (typeof self._src === 'string') {
                self._src = [
                    self._src
                ];
            }
            // Loop through the sources and pick the first one that is compatible.
            for(var i = 0; i < self._src.length; i++){
                var ext, str;
                if (self._format && self._format[i]) {
                    // If an extension was specified, use that instead.
                    ext = self._format[i];
                } else {
                    // Make sure the source is a string.
                    str = self._src[i];
                    if (typeof str !== 'string') {
                        self._emit('loaderror', null, 'Non-string found in selected audio sources - ignoring.');
                        continue;
                    }
                    // Extract the file extension from the URL or base64 data URI.
                    ext = /^data:audio\/([^;,]+);/i.exec(str);
                    if (!ext) {
                        ext = /\.([^.]+)$/.exec(str.split('?', 1)[0]);
                    }
                    if (ext) {
                        ext = ext[1].toLowerCase();
                    }
                }
                // Log a warning if no extension was found.
                if (!ext) {
                    console.warn('No file extension was found. Consider using the "format" property or specify an extension.');
                }
                // Check if this extension is available.
                if (ext && Howler1.codecs(ext)) {
                    url = self._src[i];
                    break;
                }
            }
            if (!url) {
                self._emit('loaderror', null, 'No codec support for selected audio sources.');
                return;
            }
            self._src = url;
            self._state = 'loading';
            // If the hosting page is HTTPS and the source isn't,
            // drop down to HTML5 Audio to avoid Mixed Content errors.
            if (window.location.protocol === 'https:' && url.slice(0, 5) === 'http:') {
                self._html5 = true;
                self._webAudio = false;
            }
            // Create a new sound object and add it to the pool.
            new Sound1(self);
            // Load and decode the audio data for playback.
            if (self._webAudio) {
                loadBuffer(self);
            }
            return self;
        },
        /**
     * Play a sound or resume previous playback.
     * @param  {String/Number} sprite   Sprite name for sprite playback or sound id to continue previous.
     * @param  {Boolean} internal Internal Use: true prevents event firing.
     * @return {Number}          Sound ID.
     */ play: function play(sprite, internal) {
            var self = this;
            var id = null;
            // Determine if a sprite, sound id or nothing was passed
            if (typeof sprite === 'number') {
                id = sprite;
                sprite = null;
            } else if (typeof sprite === 'string' && self._state === 'loaded' && !self._sprite[sprite]) {
                // If the passed sprite doesn't exist, do nothing.
                return null;
            } else if (typeof sprite === 'undefined') {
                // Use the default sound sprite (plays the full audio length).
                sprite = '__default';
                // Check if there is a single paused sound that isn't ended.
                // If there is, play that sound. If not, continue as usual.
                if (!self._playLock) {
                    var num = 0;
                    for(var i = 0; i < self._sounds.length; i++){
                        if (self._sounds[i]._paused && !self._sounds[i]._ended) {
                            num++;
                            id = self._sounds[i]._id;
                        }
                    }
                    if (num === 1) {
                        sprite = null;
                    } else {
                        id = null;
                    }
                }
            }
            // Get the selected node, or get one from the pool.
            var sound = id ? self._soundById(id) : self._inactiveSound();
            // If the sound doesn't exist, do nothing.
            if (!sound) {
                return null;
            }
            // Select the sprite definition.
            if (id && !sprite) {
                sprite = sound._sprite || '__default';
            }
            // If the sound hasn't loaded, we must wait to get the audio's duration.
            // We also need to wait to make sure we don't run into race conditions with
            // the order of function calls.
            if (self._state !== 'loaded') {
                // Set the sprite value on this sound.
                sound._sprite = sprite;
                // Mark this sound as not ended in case another sound is played before this one loads.
                sound._ended = false;
                // Add the sound to the queue to be played on load.
                var soundId = sound._id;
                self._queue.push({
                    event: 'play',
                    action: function action() {
                        self.play(soundId);
                    }
                });
                return soundId;
            }
            // Don't play the sound if an id was passed and it is already playing.
            if (id && !sound._paused) {
                // Trigger the play event, in order to keep iterating through queue.
                if (!internal) {
                    self._loadQueue('play');
                }
                return sound._id;
            }
            // Make sure the AudioContext isn't suspended, and resume it if it is.
            if (self._webAudio) {
                Howler1._autoResume();
            }
            // Determine how long to play for and where to start playing.
            var seek = Math.max(0, sound._seek > 0 ? sound._seek : self._sprite[sprite][0] / 1000);
            var duration = Math.max(0, (self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000 - seek);
            var timeout = duration * 1000 / Math.abs(sound._rate);
            var start = self._sprite[sprite][0] / 1000;
            var stop = (self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000;
            sound._sprite = sprite;
            // Mark the sound as ended instantly so that this async playback
            // doesn't get grabbed by another call to play while this one waits to start.
            sound._ended = false;
            // Update the parameters of the sound.
            var setParams = function setParams() {
                sound._paused = false;
                sound._seek = seek;
                sound._start = start;
                sound._stop = stop;
                sound._loop = !!(sound._loop || self._sprite[sprite][2]);
            };
            // End the sound instantly if seek is at the end.
            if (seek >= stop) {
                self._ended(sound);
                return;
            }
            // Begin the actual playback.
            var node = sound._node;
            if (self._webAudio) {
                // Fire this when the sound is ready to play to begin Web Audio playback.
                var playWebAudio = function playWebAudio() {
                    self._playLock = false;
                    setParams();
                    self._refreshBuffer(sound);
                    // Setup the playback params.
                    var vol = sound._muted || self._muted ? 0 : sound._volume;
                    node.gain.setValueAtTime(vol, Howler1.ctx.currentTime);
                    sound._playStart = Howler1.ctx.currentTime;
                    // Play the sound using the supported method.
                    if (typeof node.bufferSource.start === 'undefined') {
                        sound._loop ? node.bufferSource.noteGrainOn(0, seek, 86400) : node.bufferSource.noteGrainOn(0, seek, duration);
                    } else {
                        sound._loop ? node.bufferSource.start(0, seek, 86400) : node.bufferSource.start(0, seek, duration);
                    }
                    // Start a new timer if none is present.
                    if (timeout !== Infinity) {
                        self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);
                    }
                    if (!internal) {
                        setTimeout(function() {
                            self._emit('play', sound._id);
                            self._loadQueue();
                        }, 0);
                    }
                };
                if (Howler1.state === 'running' && Howler1.ctx.state !== 'interrupted') {
                    playWebAudio();
                } else {
                    self._playLock = true;
                    // Wait for the audio context to resume before playing.
                    self.once('resume', playWebAudio);
                    // Cancel the end timer.
                    self._clearTimer(sound._id);
                }
            } else {
                // Fire this when the sound is ready to play to begin HTML5 Audio playback.
                var playHtml5 = function playHtml5() {
                    node.currentTime = seek;
                    node.muted = sound._muted || self._muted || Howler1._muted || node.muted;
                    node.volume = sound._volume * Howler1.volume();
                    node.playbackRate = sound._rate;
                    // Some browsers will throw an error if this is called without user interaction.
                    try {
                        var play = node.play();
                        // Support older browsers that don't support promises, and thus don't have this issue.
                        if (play && typeof Promise !== 'undefined' && (_instanceof(play, Promise) || typeof play.then === 'function')) {
                            // Implements a lock to prevent DOMException: The play() request was interrupted by a call to pause().
                            self._playLock = true;
                            // Set param values immediately.
                            setParams();
                            // Releases the lock and executes queued actions.
                            play.then(function() {
                                self._playLock = false;
                                node._unlocked = true;
                                if (!internal) {
                                    self._emit('play', sound._id);
                                } else {
                                    self._loadQueue();
                                }
                            }).catch(function() {
                                self._playLock = false;
                                self._emit('playerror', sound._id, 'Playback was unable to start. This is most commonly an issue ' + 'on mobile devices and Chrome where playback was not within a user interaction.');
                                // Reset the ended and paused values.
                                sound._ended = true;
                                sound._paused = true;
                            });
                        } else if (!internal) {
                            self._playLock = false;
                            setParams();
                            self._emit('play', sound._id);
                        }
                        // Setting rate before playing won't work in IE, so we set it again here.
                        node.playbackRate = sound._rate;
                        // If the node is still paused, then we can assume there was a playback issue.
                        if (node.paused) {
                            self._emit('playerror', sound._id, 'Playback was unable to start. This is most commonly an issue ' + 'on mobile devices and Chrome where playback was not within a user interaction.');
                            return;
                        }
                        // Setup the end timer on sprites or listen for the ended event.
                        if (sprite !== '__default' || sound._loop) {
                            self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);
                        } else {
                            self._endTimers[sound._id] = function() {
                                // Fire ended on this audio node.
                                self._ended(sound);
                                // Clear this listener.
                                node.removeEventListener('ended', self._endTimers[sound._id], false);
                            };
                            node.addEventListener('ended', self._endTimers[sound._id], false);
                        }
                    } catch (err) {
                        self._emit('playerror', sound._id, err);
                    }
                };
                // If this is streaming audio, make sure the src is set and load again.
                if (node.src === 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA') {
                    node.src = self._src;
                    node.load();
                }
                // Play immediately if ready, or wait for the 'canplaythrough'e vent.
                var loadedNoReadyState = window && window.ejecta || !node.readyState && Howler1._navigator.isCocoonJS;
                if (node.readyState >= 3 || loadedNoReadyState) {
                    playHtml5();
                } else {
                    self._playLock = true;
                    self._state = 'loading';
                    var listener = function listener1() {
                        self._state = 'loaded';
                        // Begin playback.
                        playHtml5();
                        // Clear this listener.
                        node.removeEventListener(Howler1._canPlayEvent, listener, false);
                    };
                    node.addEventListener(Howler1._canPlayEvent, listener, false);
                    // Cancel the end timer.
                    self._clearTimer(sound._id);
                }
            }
            return sound._id;
        },
        /**
     * Pause playback and save current position.
     * @param  {Number} id The sound ID (empty to pause all in group).
     * @return {Howl}
     */ pause: function pause(id) {
            var self = this;
            // If the sound hasn't loaded or a play() promise is pending, add it to the load queue to pause when capable.
            if (self._state !== 'loaded' || self._playLock) {
                self._queue.push({
                    event: 'pause',
                    action: function action() {
                        self.pause(id);
                    }
                });
                return self;
            }
            // If no id is passed, get all ID's to be paused.
            var ids = self._getSoundIds(id);
            for(var i = 0; i < ids.length; i++){
                // Clear the end timer.
                self._clearTimer(ids[i]);
                // Get the sound.
                var sound = self._soundById(ids[i]);
                if (sound && !sound._paused) {
                    // Reset the seek position.
                    sound._seek = self.seek(ids[i]);
                    sound._rateSeek = 0;
                    sound._paused = true;
                    // Stop currently running fades.
                    self._stopFade(ids[i]);
                    if (sound._node) {
                        if (self._webAudio) {
                            // Make sure the sound has been created.
                            if (!sound._node.bufferSource) {
                                continue;
                            }
                            if (typeof sound._node.bufferSource.stop === 'undefined') {
                                sound._node.bufferSource.noteOff(0);
                            } else {
                                sound._node.bufferSource.stop(0);
                            }
                            // Clean up the buffer source.
                            self._cleanBuffer(sound._node);
                        } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {
                            sound._node.pause();
                        }
                    }
                }
                // Fire the pause event, unless `true` is passed as the 2nd argument.
                if (!arguments[1]) {
                    self._emit('pause', sound ? sound._id : null);
                }
            }
            return self;
        },
        /**
     * Stop playback and reset to start.
     * @param  {Number} id The sound ID (empty to stop all in group).
     * @param  {Boolean} internal Internal Use: true prevents event firing.
     * @return {Howl}
     */ stop: function stop(id, internal) {
            var self = this;
            // If the sound hasn't loaded, add it to the load queue to stop when capable.
            if (self._state !== 'loaded' || self._playLock) {
                self._queue.push({
                    event: 'stop',
                    action: function action() {
                        self.stop(id);
                    }
                });
                return self;
            }
            // If no id is passed, get all ID's to be stopped.
            var ids = self._getSoundIds(id);
            for(var i = 0; i < ids.length; i++){
                // Clear the end timer.
                self._clearTimer(ids[i]);
                // Get the sound.
                var sound = self._soundById(ids[i]);
                if (sound) {
                    // Reset the seek position.
                    sound._seek = sound._start || 0;
                    sound._rateSeek = 0;
                    sound._paused = true;
                    sound._ended = true;
                    // Stop currently running fades.
                    self._stopFade(ids[i]);
                    if (sound._node) {
                        if (self._webAudio) {
                            // Make sure the sound's AudioBufferSourceNode has been created.
                            if (sound._node.bufferSource) {
                                if (typeof sound._node.bufferSource.stop === 'undefined') {
                                    sound._node.bufferSource.noteOff(0);
                                } else {
                                    sound._node.bufferSource.stop(0);
                                }
                                // Clean up the buffer source.
                                self._cleanBuffer(sound._node);
                            }
                        } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {
                            sound._node.currentTime = sound._start || 0;
                            sound._node.pause();
                            // If this is a live stream, stop download once the audio is stopped.
                            if (sound._node.duration === Infinity) {
                                self._clearSound(sound._node);
                            }
                        }
                    }
                    if (!internal) {
                        self._emit('stop', sound._id);
                    }
                }
            }
            return self;
        },
        /**
     * Mute/unmute a single sound or all sounds in this Howl group.
     * @param  {Boolean} muted Set to true to mute and false to unmute.
     * @param  {Number} id    The sound ID to update (omit to mute/unmute all).
     * @return {Howl}
     */ mute: function mute(muted, id) {
            var self = this;
            // If the sound hasn't loaded, add it to the load queue to mute when capable.
            if (self._state !== 'loaded' || self._playLock) {
                self._queue.push({
                    event: 'mute',
                    action: function action() {
                        self.mute(muted, id);
                    }
                });
                return self;
            }
            // If applying mute/unmute to all sounds, update the group's value.
            if (typeof id === 'undefined') {
                if (typeof muted === 'boolean') {
                    self._muted = muted;
                } else {
                    return self._muted;
                }
            }
            // If no id is passed, get all ID's to be muted.
            var ids = self._getSoundIds(id);
            for(var i = 0; i < ids.length; i++){
                // Get the sound.
                var sound = self._soundById(ids[i]);
                if (sound) {
                    sound._muted = muted;
                    // Cancel active fade and set the volume to the end value.
                    if (sound._interval) {
                        self._stopFade(sound._id);
                    }
                    if (self._webAudio && sound._node) {
                        sound._node.gain.setValueAtTime(muted ? 0 : sound._volume, Howler1.ctx.currentTime);
                    } else if (sound._node) {
                        sound._node.muted = Howler1._muted ? true : muted;
                    }
                    self._emit('mute', sound._id);
                }
            }
            return self;
        },
        /**
     * Get/set the volume of this sound or of the Howl group. This method can optionally take 0, 1 or 2 arguments.
     *   volume() -> Returns the group's volume value.
     *   volume(id) -> Returns the sound id's current volume.
     *   volume(vol) -> Sets the volume of all sounds in this Howl group.
     *   volume(vol, id) -> Sets the volume of passed sound id.
     * @return {Howl/Number} Returns self or current volume.
     */ volume: function volume() {
            var self = this;
            var args = arguments;
            var vol, id;
            // Determine the values based on arguments.
            if (args.length === 0) {
                // Return the value of the groups' volume.
                return self._volume;
            } else if (args.length === 1 || args.length === 2 && typeof args[1] === 'undefined') {
                // First check if this is an ID, and if not, assume it is a new volume.
                var ids = self._getSoundIds();
                var index = ids.indexOf(args[0]);
                if (index >= 0) {
                    id = parseInt(args[0], 10);
                } else {
                    vol = parseFloat(args[0]);
                }
            } else if (args.length >= 2) {
                vol = parseFloat(args[0]);
                id = parseInt(args[1], 10);
            }
            // Update the volume or return the current volume.
            var sound;
            if (typeof vol !== 'undefined' && vol >= 0 && vol <= 1) {
                // If the sound hasn't loaded, add it to the load queue to change volume when capable.
                if (self._state !== 'loaded' || self._playLock) {
                    self._queue.push({
                        event: 'volume',
                        action: function action() {
                            self.volume.apply(self, args);
                        }
                    });
                    return self;
                }
                // Set the group volume.
                if (typeof id === 'undefined') {
                    self._volume = vol;
                }
                // Update one or all volumes.
                id = self._getSoundIds(id);
                for(var i = 0; i < id.length; i++){
                    // Get the sound.
                    sound = self._soundById(id[i]);
                    if (sound) {
                        sound._volume = vol;
                        // Stop currently running fades.
                        if (!args[2]) {
                            self._stopFade(id[i]);
                        }
                        if (self._webAudio && sound._node && !sound._muted) {
                            sound._node.gain.setValueAtTime(vol, Howler1.ctx.currentTime);
                        } else if (sound._node && !sound._muted) {
                            sound._node.volume = vol * Howler1.volume();
                        }
                        self._emit('volume', sound._id);
                    }
                }
            } else {
                sound = id ? self._soundById(id) : self._sounds[0];
                return sound ? sound._volume : 0;
            }
            return self;
        },
        /**
     * Fade a currently playing sound between two volumes (if no id is passed, all sounds will fade).
     * @param  {Number} from The value to fade from (0.0 to 1.0).
     * @param  {Number} to   The volume to fade to (0.0 to 1.0).
     * @param  {Number} len  Time in milliseconds to fade.
     * @param  {Number} id   The sound id (omit to fade all sounds).
     * @return {Howl}
     */ fade: function fade(from, to, len, id) {
            var self = this;
            // If the sound hasn't loaded, add it to the load queue to fade when capable.
            if (self._state !== 'loaded' || self._playLock) {
                self._queue.push({
                    event: 'fade',
                    action: function action() {
                        self.fade(from, to, len, id);
                    }
                });
                return self;
            }
            // Make sure the to/from/len values are numbers.
            from = Math.min(Math.max(0, parseFloat(from)), 1);
            to = Math.min(Math.max(0, parseFloat(to)), 1);
            len = parseFloat(len);
            // Set the volume to the start position.
            self.volume(from, id);
            // Fade the volume of one or all sounds.
            var ids = self._getSoundIds(id);
            for(var i = 0; i < ids.length; i++){
                // Get the sound.
                var sound = self._soundById(ids[i]);
                // Create a linear fade or fall back to timeouts with HTML5 Audio.
                if (sound) {
                    // Stop the previous fade if no sprite is being used (otherwise, volume handles this).
                    if (!id) {
                        self._stopFade(ids[i]);
                    }
                    // If we are using Web Audio, let the native methods do the actual fade.
                    if (self._webAudio && !sound._muted) {
                        var currentTime = Howler1.ctx.currentTime;
                        var end = currentTime + len / 1000;
                        sound._volume = from;
                        sound._node.gain.setValueAtTime(from, currentTime);
                        sound._node.gain.linearRampToValueAtTime(to, end);
                    }
                    self._startFadeInterval(sound, from, to, len, ids[i], typeof id === 'undefined');
                }
            }
            return self;
        },
        /**
     * Starts the internal interval to fade a sound.
     * @param  {Object} sound Reference to sound to fade.
     * @param  {Number} from The value to fade from (0.0 to 1.0).
     * @param  {Number} to   The volume to fade to (0.0 to 1.0).
     * @param  {Number} len  Time in milliseconds to fade.
     * @param  {Number} id   The sound id to fade.
     * @param  {Boolean} isGroup   If true, set the volume on the group.
     */ _startFadeInterval: function _startFadeInterval(sound, from, to, len, id, isGroup) {
            var self = this;
            var vol = from;
            var diff = to - from;
            var steps = Math.abs(diff / 0.01);
            var stepLen = Math.max(4, steps > 0 ? len / steps : len);
            var lastTick = Date.now();
            // Store the value being faded to.
            sound._fadeTo = to;
            // Update the volume value on each interval tick.
            sound._interval = setInterval(function() {
                // Update the volume based on the time since the last tick.
                var tick = (Date.now() - lastTick) / len;
                lastTick = Date.now();
                vol += diff * tick;
                // Round to within 2 decimal points.
                vol = Math.round(vol * 100) / 100;
                // Make sure the volume is in the right bounds.
                if (diff < 0) {
                    vol = Math.max(to, vol);
                } else {
                    vol = Math.min(to, vol);
                }
                // Change the volume.
                if (self._webAudio) {
                    sound._volume = vol;
                } else {
                    self.volume(vol, sound._id, true);
                }
                // Set the group's volume.
                if (isGroup) {
                    self._volume = vol;
                }
                // When the fade is complete, stop it and fire event.
                if (to < from && vol <= to || to > from && vol >= to) {
                    clearInterval(sound._interval);
                    sound._interval = null;
                    sound._fadeTo = null;
                    self.volume(to, sound._id);
                    self._emit('fade', sound._id);
                }
            }, stepLen);
        },
        /**
     * Internal method that stops the currently playing fade when
     * a new fade starts, volume is changed or the sound is stopped.
     * @param  {Number} id The sound id.
     * @return {Howl}
     */ _stopFade: function _stopFade(id) {
            var self = this;
            var sound = self._soundById(id);
            if (sound && sound._interval) {
                if (self._webAudio) {
                    sound._node.gain.cancelScheduledValues(Howler1.ctx.currentTime);
                }
                clearInterval(sound._interval);
                sound._interval = null;
                self.volume(sound._fadeTo, id);
                sound._fadeTo = null;
                self._emit('fade', id);
            }
            return self;
        },
        /**
     * Get/set the loop parameter on a sound. This method can optionally take 0, 1 or 2 arguments.
     *   loop() -> Returns the group's loop value.
     *   loop(id) -> Returns the sound id's loop value.
     *   loop(loop) -> Sets the loop value for all sounds in this Howl group.
     *   loop(loop, id) -> Sets the loop value of passed sound id.
     * @return {Howl/Boolean} Returns self or current loop value.
     */ loop: function loop() {
            var self = this;
            var args = arguments;
            var loop, id, sound;
            // Determine the values for loop and id.
            if (args.length === 0) {
                // Return the grou's loop value.
                return self._loop;
            } else if (args.length === 1) {
                if (typeof args[0] === 'boolean') {
                    loop = args[0];
                    self._loop = loop;
                } else {
                    // Return this sound's loop value.
                    sound = self._soundById(parseInt(args[0], 10));
                    return sound ? sound._loop : false;
                }
            } else if (args.length === 2) {
                loop = args[0];
                id = parseInt(args[1], 10);
            }
            // If no id is passed, get all ID's to be looped.
            var ids = self._getSoundIds(id);
            for(var i = 0; i < ids.length; i++){
                sound = self._soundById(ids[i]);
                if (sound) {
                    sound._loop = loop;
                    if (self._webAudio && sound._node && sound._node.bufferSource) {
                        sound._node.bufferSource.loop = loop;
                        if (loop) {
                            sound._node.bufferSource.loopStart = sound._start || 0;
                            sound._node.bufferSource.loopEnd = sound._stop;
                            // If playing, restart playback to ensure looping updates.
                            if (self.playing(ids[i])) {
                                self.pause(ids[i], true);
                                self.play(ids[i], true);
                            }
                        }
                    }
                }
            }
            return self;
        },
        /**
     * Get/set the playback rate of a sound. This method can optionally take 0, 1 or 2 arguments.
     *   rate() -> Returns the first sound node's current playback rate.
     *   rate(id) -> Returns the sound id's current playback rate.
     *   rate(rate) -> Sets the playback rate of all sounds in this Howl group.
     *   rate(rate, id) -> Sets the playback rate of passed sound id.
     * @return {Howl/Number} Returns self or the current playback rate.
     */ rate: function rate() {
            var self = this;
            var args = arguments;
            var rate, id;
            // Determine the values based on arguments.
            if (args.length === 0) {
                // We will simply return the current rate of the first node.
                id = self._sounds[0]._id;
            } else if (args.length === 1) {
                // First check if this is an ID, and if not, assume it is a new rate value.
                var ids = self._getSoundIds();
                var index = ids.indexOf(args[0]);
                if (index >= 0) {
                    id = parseInt(args[0], 10);
                } else {
                    rate = parseFloat(args[0]);
                }
            } else if (args.length === 2) {
                rate = parseFloat(args[0]);
                id = parseInt(args[1], 10);
            }
            // Update the playback rate or return the current value.
            var sound;
            if (typeof rate === 'number') {
                // If the sound hasn't loaded, add it to the load queue to change playback rate when capable.
                if (self._state !== 'loaded' || self._playLock) {
                    self._queue.push({
                        event: 'rate',
                        action: function action() {
                            self.rate.apply(self, args);
                        }
                    });
                    return self;
                }
                // Set the group rate.
                if (typeof id === 'undefined') {
                    self._rate = rate;
                }
                // Update one or all volumes.
                id = self._getSoundIds(id);
                for(var i = 0; i < id.length; i++){
                    // Get the sound.
                    sound = self._soundById(id[i]);
                    if (sound) {
                        // Keep track of our position when the rate changed and update the playback
                        // start position so we can properly adjust the seek position for time elapsed.
                        if (self.playing(id[i])) {
                            sound._rateSeek = self.seek(id[i]);
                            sound._playStart = self._webAudio ? Howler1.ctx.currentTime : sound._playStart;
                        }
                        sound._rate = rate;
                        // Change the playback rate.
                        if (self._webAudio && sound._node && sound._node.bufferSource) {
                            sound._node.bufferSource.playbackRate.setValueAtTime(rate, Howler1.ctx.currentTime);
                        } else if (sound._node) {
                            sound._node.playbackRate = rate;
                        }
                        // Reset the timers.
                        var seek = self.seek(id[i]);
                        var duration = (self._sprite[sound._sprite][0] + self._sprite[sound._sprite][1]) / 1000 - seek;
                        var timeout = duration * 1000 / Math.abs(sound._rate);
                        // Start a new end timer if sound is already playing.
                        if (self._endTimers[id[i]] || !sound._paused) {
                            self._clearTimer(id[i]);
                            self._endTimers[id[i]] = setTimeout(self._ended.bind(self, sound), timeout);
                        }
                        self._emit('rate', sound._id);
                    }
                }
            } else {
                sound = self._soundById(id);
                return sound ? sound._rate : self._rate;
            }
            return self;
        },
        /**
     * Get/set the seek position of a sound. This method can optionally take 0, 1 or 2 arguments.
     *   seek() -> Returns the first sound node's current seek position.
     *   seek(id) -> Returns the sound id's current seek position.
     *   seek(seek) -> Sets the seek position of the first sound node.
     *   seek(seek, id) -> Sets the seek position of passed sound id.
     * @return {Howl/Number} Returns self or the current seek position.
     */ seek: function seek() {
            var self = this;
            var args = arguments;
            var seek, id;
            // Determine the values based on arguments.
            if (args.length === 0) {
                // We will simply return the current position of the first node.
                if (self._sounds.length) {
                    id = self._sounds[0]._id;
                }
            } else if (args.length === 1) {
                // First check if this is an ID, and if not, assume it is a new seek position.
                var ids = self._getSoundIds();
                var index = ids.indexOf(args[0]);
                if (index >= 0) {
                    id = parseInt(args[0], 10);
                } else if (self._sounds.length) {
                    id = self._sounds[0]._id;
                    seek = parseFloat(args[0]);
                }
            } else if (args.length === 2) {
                seek = parseFloat(args[0]);
                id = parseInt(args[1], 10);
            }
            // If there is no ID, bail out.
            if (typeof id === 'undefined') {
                return 0;
            }
            // If the sound hasn't loaded, add it to the load queue to seek when capable.
            if (typeof seek === 'number' && (self._state !== 'loaded' || self._playLock)) {
                self._queue.push({
                    event: 'seek',
                    action: function action() {
                        self.seek.apply(self, args);
                    }
                });
                return self;
            }
            // Get the sound.
            var sound = self._soundById(id);
            if (sound) {
                if (typeof seek === 'number' && seek >= 0) {
                    // Pause the sound and update position for restarting playback.
                    var playing = self.playing(id);
                    if (playing) {
                        self.pause(id, true);
                    }
                    // Move the position of the track and cancel timer.
                    sound._seek = seek;
                    sound._ended = false;
                    self._clearTimer(id);
                    // Update the seek position for HTML5 Audio.
                    if (!self._webAudio && sound._node && !isNaN(sound._node.duration)) {
                        sound._node.currentTime = seek;
                    }
                    // Seek and emit when ready.
                    var seekAndEmit = function seekAndEmit() {
                        // Restart the playback if the sound was playing.
                        if (playing) {
                            self.play(id, true);
                        }
                        self._emit('seek', id);
                    };
                    // Wait for the play lock to be unset before emitting (HTML5 Audio).
                    if (playing && !self._webAudio) {
                        var emitSeek = function emitSeek1() {
                            if (!self._playLock) {
                                seekAndEmit();
                            } else {
                                setTimeout(emitSeek, 0);
                            }
                        };
                        setTimeout(emitSeek, 0);
                    } else {
                        seekAndEmit();
                    }
                } else {
                    if (self._webAudio) {
                        var realTime = self.playing(id) ? Howler1.ctx.currentTime - sound._playStart : 0;
                        var rateSeek = sound._rateSeek ? sound._rateSeek - sound._seek : 0;
                        return sound._seek + (rateSeek + realTime * Math.abs(sound._rate));
                    } else {
                        return sound._node.currentTime;
                    }
                }
            }
            return self;
        },
        /**
     * Check if a specific sound is currently playing or not (if id is provided), or check if at least one of the sounds in the group is playing or not.
     * @param  {Number}  id The sound id to check. If none is passed, the whole sound group is checked.
     * @return {Boolean} True if playing and false if not.
     */ playing: function playing(id) {
            var self = this;
            // Check the passed sound ID (if any).
            if (typeof id === 'number') {
                var sound = self._soundById(id);
                return sound ? !sound._paused : false;
            }
            // Otherwise, loop through all sounds and check if any are playing.
            for(var i = 0; i < self._sounds.length; i++){
                if (!self._sounds[i]._paused) {
                    return true;
                }
            }
            return false;
        },
        /**
     * Get the duration of this sound. Passing a sound id will return the sprite duration.
     * @param  {Number} id The sound id to check. If none is passed, return full source duration.
     * @return {Number} Audio duration in seconds.
     */ duration: function duration(id) {
            var self = this;
            var duration = self._duration;
            // If we pass an ID, get the sound and return the sprite length.
            var sound = self._soundById(id);
            if (sound) {
                duration = self._sprite[sound._sprite][1] / 1000;
            }
            return duration;
        },
        /**
     * Returns the current loaded state of this Howl.
     * @return {String} 'unloaded', 'loading', 'loaded'
     */ state: function state() {
            return this._state;
        },
        /**
     * Unload and destroy the current Howl object.
     * This will immediately stop all sound instances attached to this group.
     */ unload: function unload() {
            var self = this;
            // Stop playing any active sounds.
            var sounds = self._sounds;
            for(var i = 0; i < sounds.length; i++){
                // Stop the sound if it is currently playing.
                if (!sounds[i]._paused) {
                    self.stop(sounds[i]._id);
                }
                // Remove the source or disconnect.
                if (!self._webAudio) {
                    // Set the source to 0-second silence to stop any downloading (except in IE).
                    self._clearSound(sounds[i]._node);
                    // Remove any event listeners.
                    sounds[i]._node.removeEventListener('error', sounds[i]._errorFn, false);
                    sounds[i]._node.removeEventListener(Howler1._canPlayEvent, sounds[i]._loadFn, false);
                    sounds[i]._node.removeEventListener('ended', sounds[i]._endFn, false);
                    // Release the Audio object back to the pool.
                    Howler1._releaseHtml5Audio(sounds[i]._node);
                }
                // Empty out all of the nodes.
                delete sounds[i]._node;
                // Make sure all timers are cleared out.
                self._clearTimer(sounds[i]._id);
            }
            // Remove the references in the global Howler object.
            var index = Howler1._howls.indexOf(self);
            if (index >= 0) {
                Howler1._howls.splice(index, 1);
            }
            // Delete this sound from the cache (if no other Howl is using it).
            var remCache = true;
            for(i = 0; i < Howler1._howls.length; i++){
                if (Howler1._howls[i]._src === self._src || self._src.indexOf(Howler1._howls[i]._src) >= 0) {
                    remCache = false;
                    break;
                }
            }
            if (cache && remCache) {
                delete cache[self._src];
            }
            // Clear global errors.
            Howler1.noAudio = false;
            // Clear out `self`.
            self._state = 'unloaded';
            self._sounds = [];
            self = null;
            return null;
        },
        /**
     * Listen to a custom event.
     * @param  {String}   event Event name.
     * @param  {Function} fn    Listener to call.
     * @param  {Number}   id    (optional) Only listen to events for this sound.
     * @param  {Number}   once  (INTERNAL) Marks event to fire only once.
     * @return {Howl}
     */ on: function on(event, fn, id, once) {
            var self = this;
            var events = self['_on' + event];
            if (typeof fn === 'function') {
                events.push(once ? {
                    id: id,
                    fn: fn,
                    once: once
                } : {
                    id: id,
                    fn: fn
                });
            }
            return self;
        },
        /**
     * Remove a custom event. Call without parameters to remove all events.
     * @param  {String}   event Event name.
     * @param  {Function} fn    Listener to remove. Leave empty to remove all.
     * @param  {Number}   id    (optional) Only remove events for this sound.
     * @return {Howl}
     */ off: function off(event, fn, id) {
            var self = this;
            var events = self['_on' + event];
            var i = 0;
            // Allow passing just an event and ID.
            if (typeof fn === 'number') {
                id = fn;
                fn = null;
            }
            if (fn || id) {
                // Loop through event store and remove the passed function.
                for(i = 0; i < events.length; i++){
                    var isId = id === events[i].id;
                    if (fn === events[i].fn && isId || !fn && isId) {
                        events.splice(i, 1);
                        break;
                    }
                }
            } else if (event) {
                // Clear out all events of this type.
                self['_on' + event] = [];
            } else {
                // Clear out all events of every type.
                var keys = Object.keys(self);
                for(i = 0; i < keys.length; i++){
                    if (keys[i].indexOf('_on') === 0 && Array.isArray(self[keys[i]])) {
                        self[keys[i]] = [];
                    }
                }
            }
            return self;
        },
        /**
     * Listen to a custom event and remove it once fired.
     * @param  {String}   event Event name.
     * @param  {Function} fn    Listener to call.
     * @param  {Number}   id    (optional) Only listen to events for this sound.
     * @return {Howl}
     */ once: function once(event, fn, id) {
            var self = this;
            // Setup the event listener.
            self.on(event, fn, id, 1);
            return self;
        },
        /**
     * Emit all events of a specific type and pass the sound id.
     * @param  {String} event Event name.
     * @param  {Number} id    Sound ID.
     * @param  {Number} msg   Message to go with event.
     * @return {Howl}
     */ _emit: function _emit(event, id, msg) {
            var self = this;
            var events = self['_on' + event];
            // Loop through event store and fire all functions.
            for(var i = events.length - 1; i >= 0; i--){
                // Only fire the listener if the correct ID is used.
                if (!events[i].id || events[i].id === id || event === 'load') {
                    setTimeout((function(fn) {
                        fn.call(this, id, msg);
                    }).bind(self, events[i].fn), 0);
                    // If this event was setup with `once`, remove it.
                    if (events[i].once) {
                        self.off(event, events[i].fn, events[i].id);
                    }
                }
            }
            // Pass the event type into load queue so that it can continue stepping.
            self._loadQueue(event);
            return self;
        },
        /**
     * Queue of actions initiated before the sound has loaded.
     * These will be called in sequence, with the next only firing
     * after the previous has finished executing (even if async like play).
     * @return {Howl}
     */ _loadQueue: function _loadQueue(event) {
            var self = this;
            if (self._queue.length > 0) {
                var task = self._queue[0];
                // Remove this task if a matching event was passed.
                if (task.event === event) {
                    self._queue.shift();
                    self._loadQueue();
                }
                // Run the task if no event type is passed.
                if (!event) {
                    task.action();
                }
            }
            return self;
        },
        /**
     * Fired when playback ends at the end of the duration.
     * @param  {Sound} sound The sound object to work with.
     * @return {Howl}
     */ _ended: function _ended(sound) {
            var self = this;
            var sprite = sound._sprite;
            // If we are using IE and there was network latency we may be clipping
            // audio before it completes playing. Lets check the node to make sure it
            // believes it has completed, before ending the playback.
            if (!self._webAudio && sound._node && !sound._node.paused && !sound._node.ended && sound._node.currentTime < sound._stop) {
                setTimeout(self._ended.bind(self, sound), 100);
                return self;
            }
            // Should this sound loop?
            var loop = !!(sound._loop || self._sprite[sprite][2]);
            // Fire the ended event.
            self._emit('end', sound._id);
            // Restart the playback for HTML5 Audio loop.
            if (!self._webAudio && loop) {
                self.stop(sound._id, true).play(sound._id);
            }
            // Restart this timer if on a Web Audio loop.
            if (self._webAudio && loop) {
                self._emit('play', sound._id);
                sound._seek = sound._start || 0;
                sound._rateSeek = 0;
                sound._playStart = Howler1.ctx.currentTime;
                var timeout = (sound._stop - sound._start) * 1000 / Math.abs(sound._rate);
                self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);
            }
            // Mark the node as paused.
            if (self._webAudio && !loop) {
                sound._paused = true;
                sound._ended = true;
                sound._seek = sound._start || 0;
                sound._rateSeek = 0;
                self._clearTimer(sound._id);
                // Clean up the buffer source.
                self._cleanBuffer(sound._node);
                // Attempt to auto-suspend AudioContext if no sounds are still playing.
                Howler1._autoSuspend();
            }
            // When using a sprite, end the track.
            if (!self._webAudio && !loop) {
                self.stop(sound._id, true);
            }
            return self;
        },
        /**
     * Clear the end timer for a sound playback.
     * @param  {Number} id The sound ID.
     * @return {Howl}
     */ _clearTimer: function _clearTimer(id) {
            var self = this;
            if (self._endTimers[id]) {
                // Clear the timeout or remove the ended listener.
                if (typeof self._endTimers[id] !== 'function') {
                    clearTimeout(self._endTimers[id]);
                } else {
                    var sound = self._soundById(id);
                    if (sound && sound._node) {
                        sound._node.removeEventListener('ended', self._endTimers[id], false);
                    }
                }
                delete self._endTimers[id];
            }
            return self;
        },
        /**
     * Return the sound identified by this ID, or return null.
     * @param  {Number} id Sound ID
     * @return {Object}    Sound object or null.
     */ _soundById: function _soundById(id) {
            var self = this;
            // Loop through all sounds and find the one with this ID.
            for(var i = 0; i < self._sounds.length; i++){
                if (id === self._sounds[i]._id) {
                    return self._sounds[i];
                }
            }
            return null;
        },
        /**
     * Return an inactive sound from the pool or create a new one.
     * @return {Sound} Sound playback object.
     */ _inactiveSound: function _inactiveSound() {
            var self = this;
            self._drain();
            // Find the first inactive node to recycle.
            for(var i = 0; i < self._sounds.length; i++){
                if (self._sounds[i]._ended) {
                    return self._sounds[i].reset();
                }
            }
            // If no inactive node was found, create a new one.
            return new Sound1(self);
        },
        /**
     * Drain excess inactive sounds from the pool.
     */ _drain: function _drain() {
            var self = this;
            var limit = self._pool;
            var cnt = 0;
            var i = 0;
            // If there are less sounds than the max pool size, we are done.
            if (self._sounds.length < limit) {
                return;
            }
            // Count the number of inactive sounds.
            for(i = 0; i < self._sounds.length; i++){
                if (self._sounds[i]._ended) {
                    cnt++;
                }
            }
            // Remove excess inactive sounds, going in reverse order.
            for(i = self._sounds.length - 1; i >= 0; i--){
                if (cnt <= limit) {
                    return;
                }
                if (self._sounds[i]._ended) {
                    // Disconnect the audio source when using Web Audio.
                    if (self._webAudio && self._sounds[i]._node) {
                        self._sounds[i]._node.disconnect(0);
                    }
                    // Remove sounds until we have the pool size.
                    self._sounds.splice(i, 1);
                    cnt--;
                }
            }
        },
        /**
     * Get all ID's from the sounds pool.
     * @param  {Number} id Only return one ID if one is passed.
     * @return {Array}    Array of IDs.
     */ _getSoundIds: function _getSoundIds(id) {
            var self = this;
            if (typeof id === 'undefined') {
                var ids = [];
                for(var i = 0; i < self._sounds.length; i++){
                    ids.push(self._sounds[i]._id);
                }
                return ids;
            } else {
                return [
                    id
                ];
            }
        },
        /**
     * Load the sound back into the buffer source.
     * @param  {Sound} sound The sound object to work with.
     * @return {Howl}
     */ _refreshBuffer: function _refreshBuffer(sound) {
            var self = this;
            // Setup the buffer source for playback.
            sound._node.bufferSource = Howler1.ctx.createBufferSource();
            sound._node.bufferSource.buffer = cache[self._src];
            // Connect to the correct node.
            if (sound._panner) {
                sound._node.bufferSource.connect(sound._panner);
            } else {
                sound._node.bufferSource.connect(sound._node);
            }
            // Setup looping and playback rate.
            sound._node.bufferSource.loop = sound._loop;
            if (sound._loop) {
                sound._node.bufferSource.loopStart = sound._start || 0;
                sound._node.bufferSource.loopEnd = sound._stop || 0;
            }
            sound._node.bufferSource.playbackRate.setValueAtTime(sound._rate, Howler1.ctx.currentTime);
            return self;
        },
        /**
     * Prevent memory leaks by cleaning up the buffer source after playback.
     * @param  {Object} node Sound's audio node containing the buffer source.
     * @return {Howl}
     */ _cleanBuffer: function _cleanBuffer(node) {
            var self = this;
            var isIOS = Howler1._navigator && Howler1._navigator.vendor.indexOf('Apple') >= 0;
            if (Howler1._scratchBuffer && node.bufferSource) {
                node.bufferSource.onended = null;
                node.bufferSource.disconnect(0);
                if (isIOS) {
                    try {
                        node.bufferSource.buffer = Howler1._scratchBuffer;
                    } catch (e) {}
                }
            }
            node.bufferSource = null;
            return self;
        },
        /**
     * Set the source to a 0-second silence to stop any downloading (except in IE).
     * @param  {Object} node Audio node to clear.
     */ _clearSound: function _clearSound(node) {
            var checkIE = /MSIE |Trident\//.test(Howler1._navigator && Howler1._navigator.userAgent);
            if (!checkIE) {
                node.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA';
            }
        }
    };
    /** Single Sound Methods **/ /***************************************************************************/ /**
   * Setup the sound object, which each node attached to a Howl group is contained in.
   * @param {Object} howl The Howl parent group.
   */ var Sound1 = function Sound1(howl) {
        this._parent = howl;
        this.init();
    };
    Sound1.prototype = {
        /**
     * Initialize a new Sound object.
     * @return {Sound}
     */ init: function init() {
            var self = this;
            var parent = self._parent;
            // Setup the default parameters.
            self._muted = parent._muted;
            self._loop = parent._loop;
            self._volume = parent._volume;
            self._rate = parent._rate;
            self._seek = 0;
            self._paused = true;
            self._ended = true;
            self._sprite = '__default';
            // Generate a unique ID for this sound.
            self._id = ++Howler1._counter;
            // Add itself to the parent's pool.
            parent._sounds.push(self);
            // Create the new node.
            self.create();
            return self;
        },
        /**
     * Create and setup a new sound object, whether HTML5 Audio or Web Audio.
     * @return {Sound}
     */ create: function create() {
            var self = this;
            var parent = self._parent;
            var volume = Howler1._muted || self._muted || self._parent._muted ? 0 : self._volume;
            if (parent._webAudio) {
                // Create the gain node for controlling volume (the source will connect to this).
                self._node = typeof Howler1.ctx.createGain === 'undefined' ? Howler1.ctx.createGainNode() : Howler1.ctx.createGain();
                self._node.gain.setValueAtTime(volume, Howler1.ctx.currentTime);
                self._node.paused = true;
                self._node.connect(Howler1.masterGain);
            } else if (!Howler1.noAudio) {
                // Get an unlocked Audio object from the pool.
                self._node = Howler1._obtainHtml5Audio();
                // Listen for errors (http://dev.w3.org/html5/spec-author-view/spec.html#mediaerror).
                self._errorFn = self._errorListener.bind(self);
                self._node.addEventListener('error', self._errorFn, false);
                // Listen for 'canplaythrough' event to let us know the sound is ready.
                self._loadFn = self._loadListener.bind(self);
                self._node.addEventListener(Howler1._canPlayEvent, self._loadFn, false);
                // Listen for the 'ended' event on the sound to account for edge-case where
                // a finite sound has a duration of Infinity.
                self._endFn = self._endListener.bind(self);
                self._node.addEventListener('ended', self._endFn, false);
                // Setup the new audio node.
                self._node.src = parent._src;
                self._node.preload = parent._preload === true ? 'auto' : parent._preload;
                self._node.volume = volume * Howler1.volume();
                // Begin loading the source.
                self._node.load();
            }
            return self;
        },
        /**
     * Reset the parameters of this sound to the original state (for recycle).
     * @return {Sound}
     */ reset: function reset() {
            var self = this;
            var parent = self._parent;
            // Reset all of the parameters of this sound.
            self._muted = parent._muted;
            self._loop = parent._loop;
            self._volume = parent._volume;
            self._rate = parent._rate;
            self._seek = 0;
            self._rateSeek = 0;
            self._paused = true;
            self._ended = true;
            self._sprite = '__default';
            // Generate a new ID so that it isn't confused with the previous sound.
            self._id = ++Howler1._counter;
            return self;
        },
        /**
     * HTML5 Audio error listener callback.
     */ _errorListener: function _errorListener() {
            var self = this;
            // Fire an error event and pass back the code.
            self._parent._emit('loaderror', self._id, self._node.error ? self._node.error.code : 0);
            // Clear the event listener.
            self._node.removeEventListener('error', self._errorFn, false);
        },
        /**
     * HTML5 Audio canplaythrough listener callback.
     */ _loadListener: function _loadListener() {
            var self = this;
            var parent = self._parent;
            // Round up the duration to account for the lower precision in HTML5 Audio.
            parent._duration = Math.ceil(self._node.duration * 10) / 10;
            // Setup a sprite if none is defined.
            if (Object.keys(parent._sprite).length === 0) {
                parent._sprite = {
                    __default: [
                        0,
                        parent._duration * 1000
                    ]
                };
            }
            if (parent._state !== 'loaded') {
                parent._state = 'loaded';
                parent._emit('load');
                parent._loadQueue();
            }
            // Clear the event listener.
            self._node.removeEventListener(Howler1._canPlayEvent, self._loadFn, false);
        },
        /**
     * HTML5 Audio ended listener callback.
     */ _endListener: function _endListener() {
            var self = this;
            var parent = self._parent;
            // Only handle the `ended`` event if the duration is Infinity.
            if (parent._duration === Infinity) {
                // Update the parent duration to match the real audio duration.
                // Round up the duration to account for the lower precision in HTML5 Audio.
                parent._duration = Math.ceil(self._node.duration * 10) / 10;
                // Update the sprite that corresponds to the real duration.
                if (parent._sprite.__default[1] === Infinity) {
                    parent._sprite.__default[1] = parent._duration * 1000;
                }
                // Run the regular ended method.
                parent._ended(self);
            }
            // Clear the event listener since the duration is now correct.
            self._node.removeEventListener('ended', self._endFn, false);
        }
    };
    /** Helper Methods **/ /***************************************************************************/ var cache = {};
    /**
   * Buffer a sound from URL, Data URI or cache and decode to audio source (Web Audio API).
   * @param  {Howl} self
   */ var loadBuffer = function loadBuffer(self) {
        var url = self._src;
        // Check if the buffer has already been cached and use it instead.
        if (cache[url]) {
            // Set the duration from the cache.
            self._duration = cache[url].duration;
            // Load the sound into this Howl.
            loadSound(self);
            return;
        }
        if (/^data:[^;]+;base64,/.test(url)) {
            // Decode the base64 data URI without XHR, since some browsers don't support it.
            var data = atob(url.split(',')[1]);
            var dataView = new Uint8Array(data.length);
            for(var i = 0; i < data.length; ++i){
                dataView[i] = data.charCodeAt(i);
            }
            decodeAudioData(dataView.buffer, self);
        } else {
            // Load the buffer from the URL.
            var xhr = new XMLHttpRequest();
            xhr.open(self._xhr.method, url, true);
            xhr.withCredentials = self._xhr.withCredentials;
            xhr.responseType = 'arraybuffer';
            // Apply any custom headers to the request.
            if (self._xhr.headers) {
                Object.keys(self._xhr.headers).forEach(function(key) {
                    xhr.setRequestHeader(key, self._xhr.headers[key]);
                });
            }
            xhr.onload = function() {
                // Make sure we get a successful response back.
                var code = (xhr.status + '')[0];
                if (code !== '0' && code !== '2' && code !== '3') {
                    self._emit('loaderror', null, 'Failed loading audio file with status: ' + xhr.status + '.');
                    return;
                }
                decodeAudioData(xhr.response, self);
            };
            xhr.onerror = function() {
                // If there is an error, switch to HTML5 Audio.
                if (self._webAudio) {
                    self._html5 = true;
                    self._webAudio = false;
                    self._sounds = [];
                    delete cache[url];
                    self.load();
                }
            };
            safeXhrSend(xhr);
        }
    };
    /**
   * Send the XHR request wrapped in a try/catch.
   * @param  {Object} xhr XHR to send.
   */ var safeXhrSend = function safeXhrSend(xhr) {
        try {
            xhr.send();
        } catch (e) {
            xhr.onerror();
        }
    };
    /**
   * Decode audio data from an array buffer.
   * @param  {ArrayBuffer} arraybuffer The audio data.
   * @param  {Howl}        self
   */ var decodeAudioData = function decodeAudioData(arraybuffer, self) {
        // Fire a load error if something broke.
        var error = function error() {
            self._emit('loaderror', null, 'Decoding audio data failed.');
        };
        // Load the sound on success.
        var success = function success(buffer) {
            if (buffer && self._sounds.length > 0) {
                cache[self._src] = buffer;
                loadSound(self, buffer);
            } else {
                error();
            }
        };
        // Decode the buffer into an audio source.
        if (typeof Promise !== 'undefined' && Howler1.ctx.decodeAudioData.length === 1) {
            Howler1.ctx.decodeAudioData(arraybuffer).then(success).catch(error);
        } else {
            Howler1.ctx.decodeAudioData(arraybuffer, success, error);
        }
    };
    /**
   * Sound is now loaded, so finish setting everything up and fire the loaded event.
   * @param  {Howl} self
   * @param  {Object} buffer The decoded buffer sound source.
   */ var loadSound = function loadSound(self, buffer) {
        // Set the duration.
        if (buffer && !self._duration) {
            self._duration = buffer.duration;
        }
        // Setup a sprite if none is defined.
        if (Object.keys(self._sprite).length === 0) {
            self._sprite = {
                __default: [
                    0,
                    self._duration * 1000
                ]
            };
        }
        // Fire the loaded event.
        if (self._state !== 'loaded') {
            self._state = 'loaded';
            self._emit('load');
            self._loadQueue();
        }
    };
    /**
   * Setup the audio context when available, or switch to HTML5 Audio mode.
   */ var setupAudioContext = function setupAudioContext() {
        // If we have already detected that Web Audio isn't supported, don't run this step again.
        if (!Howler1.usingWebAudio) {
            return;
        }
        // Check if we are using Web Audio and setup the AudioContext if we are.
        try {
            if (typeof AudioContext !== 'undefined') {
                Howler1.ctx = new AudioContext();
            } else if (typeof webkitAudioContext !== 'undefined') {
                Howler1.ctx = new webkitAudioContext();
            } else {
                Howler1.usingWebAudio = false;
            }
        } catch (e) {
            Howler1.usingWebAudio = false;
        }
        // If the audio context creation still failed, set using web audio to false.
        if (!Howler1.ctx) {
            Howler1.usingWebAudio = false;
        }
        // Check if a webview is being used on iOS8 or earlier (rather than the browser).
        // If it is, disable Web Audio as it causes crashing.
        var iOS = /iP(hone|od|ad)/.test(Howler1._navigator && Howler1._navigator.platform);
        var appVersion = Howler1._navigator && Howler1._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);
        var version = appVersion ? parseInt(appVersion[1], 10) : null;
        if (iOS && version && version < 9) {
            var safari = /safari/.test(Howler1._navigator && Howler1._navigator.userAgent.toLowerCase());
            if (Howler1._navigator && !safari) {
                Howler1.usingWebAudio = false;
            }
        }
        // Create and expose the master GainNode when using Web Audio (useful for plugins or advanced usage).
        if (Howler1.usingWebAudio) {
            Howler1.masterGain = typeof Howler1.ctx.createGain === 'undefined' ? Howler1.ctx.createGainNode() : Howler1.ctx.createGain();
            Howler1.masterGain.gain.setValueAtTime(Howler1._muted ? 0 : Howler1._volume, Howler1.ctx.currentTime);
            Howler1.masterGain.connect(Howler1.ctx.destination);
        }
        // Re-run the setup on Howler.
        Howler1._setup();
    };
    // Add support for AMD (Asynchronous Module Definition) libraries such as require.js.
    if (typeof define === 'function' && define.amd) {
        define([], function() {
            return {
                Howler: Howler1,
                Howl: Howl1
            };
        });
    }
    // Add support for CommonJS libraries such as browserify.
    if (typeof exports !== 'undefined') {
        exports.Howler = Howler1;
        exports.Howl = Howl1;
    }
    // Add to global in Node.js (for testing, etc).
    if (typeof global !== 'undefined') {
        global.HowlerGlobal = HowlerGlobal1;
        global.Howler = Howler1;
        global.Howl = Howl1;
        global.Sound = Sound1;
    } else if (typeof window !== 'undefined') {
        window.HowlerGlobal = HowlerGlobal1;
        window.Howler = Howler1;
        window.Howl = Howl1;
        window.Sound = Sound1;
    }
})();
/*!
 *  Spatial Plugin - Adds support for stereo and 3D audio where Web Audio is supported.
 *
 *  howler.js v2.2.3
 *  howlerjs.com
 *
 *  (c) 2013-2020, James Simpson of GoldFire Studios
 *  goldfirestudios.com
 *
 *  MIT License
 */ (function() {
    'use strict';
    // Setup default properties.
    HowlerGlobal.prototype._pos = [
        0,
        0,
        0
    ];
    HowlerGlobal.prototype._orientation = [
        0,
        0,
        -1,
        0,
        1,
        0
    ];
    /** Global Methods **/ /***************************************************************************/ /**
   * Helper method to update the stereo panning position of all current Howls.
   * Future Howls will not use this value unless explicitly set.
   * @param  {Number} pan A value of -1.0 is all the way left and 1.0 is all the way right.
   * @return {Howler/Number}     Self or current stereo panning value.
   */ HowlerGlobal.prototype.stereo = function(pan) {
        var self = this;
        // Stop right here if not using Web Audio.
        if (!self.ctx || !self.ctx.listener) {
            return self;
        }
        // Loop through all Howls and update their stereo panning.
        for(var i = self._howls.length - 1; i >= 0; i--){
            self._howls[i].stereo(pan);
        }
        return self;
    };
    /**
   * Get/set the position of the listener in 3D cartesian space. Sounds using
   * 3D position will be relative to the listener's position.
   * @param  {Number} x The x-position of the listener.
   * @param  {Number} y The y-position of the listener.
   * @param  {Number} z The z-position of the listener.
   * @return {Howler/Array}   Self or current listener position.
   */ HowlerGlobal.prototype.pos = function(x, y, z) {
        var self = this;
        // Stop right here if not using Web Audio.
        if (!self.ctx || !self.ctx.listener) {
            return self;
        }
        // Set the defaults for optional 'y' & 'z'.
        y = typeof y !== 'number' ? self._pos[1] : y;
        z = typeof z !== 'number' ? self._pos[2] : z;
        if (typeof x === 'number') {
            self._pos = [
                x,
                y,
                z
            ];
            if (typeof self.ctx.listener.positionX !== 'undefined') {
                self.ctx.listener.positionX.setTargetAtTime(self._pos[0], Howler.ctx.currentTime, 0.1);
                self.ctx.listener.positionY.setTargetAtTime(self._pos[1], Howler.ctx.currentTime, 0.1);
                self.ctx.listener.positionZ.setTargetAtTime(self._pos[2], Howler.ctx.currentTime, 0.1);
            } else {
                self.ctx.listener.setPosition(self._pos[0], self._pos[1], self._pos[2]);
            }
        } else {
            return self._pos;
        }
        return self;
    };
    /**
   * Get/set the direction the listener is pointing in the 3D cartesian space.
   * A front and up vector must be provided. The front is the direction the
   * face of the listener is pointing, and up is the direction the top of the
   * listener is pointing. Thus, these values are expected to be at right angles
   * from each other.
   * @param  {Number} x   The x-orientation of the listener.
   * @param  {Number} y   The y-orientation of the listener.
   * @param  {Number} z   The z-orientation of the listener.
   * @param  {Number} xUp The x-orientation of the top of the listener.
   * @param  {Number} yUp The y-orientation of the top of the listener.
   * @param  {Number} zUp The z-orientation of the top of the listener.
   * @return {Howler/Array}     Returns self or the current orientation vectors.
   */ HowlerGlobal.prototype.orientation = function(x, y, z, xUp, yUp, zUp) {
        var self = this;
        // Stop right here if not using Web Audio.
        if (!self.ctx || !self.ctx.listener) {
            return self;
        }
        // Set the defaults for optional 'y' & 'z'.
        var or = self._orientation;
        y = typeof y !== 'number' ? or[1] : y;
        z = typeof z !== 'number' ? or[2] : z;
        xUp = typeof xUp !== 'number' ? or[3] : xUp;
        yUp = typeof yUp !== 'number' ? or[4] : yUp;
        zUp = typeof zUp !== 'number' ? or[5] : zUp;
        if (typeof x === 'number') {
            self._orientation = [
                x,
                y,
                z,
                xUp,
                yUp,
                zUp
            ];
            if (typeof self.ctx.listener.forwardX !== 'undefined') {
                self.ctx.listener.forwardX.setTargetAtTime(x, Howler.ctx.currentTime, 0.1);
                self.ctx.listener.forwardY.setTargetAtTime(y, Howler.ctx.currentTime, 0.1);
                self.ctx.listener.forwardZ.setTargetAtTime(z, Howler.ctx.currentTime, 0.1);
                self.ctx.listener.upX.setTargetAtTime(xUp, Howler.ctx.currentTime, 0.1);
                self.ctx.listener.upY.setTargetAtTime(yUp, Howler.ctx.currentTime, 0.1);
                self.ctx.listener.upZ.setTargetAtTime(zUp, Howler.ctx.currentTime, 0.1);
            } else {
                self.ctx.listener.setOrientation(x, y, z, xUp, yUp, zUp);
            }
        } else {
            return or;
        }
        return self;
    };
    /** Group Methods **/ /***************************************************************************/ /**
   * Add new properties to the core init.
   * @param  {Function} _super Core init method.
   * @return {Howl}
   */ Howl.prototype.init = function(_super) {
        return function(o) {
            var self = this;
            // Setup user-defined default properties.
            self._orientation = o.orientation || [
                1,
                0,
                0
            ];
            self._stereo = o.stereo || null;
            self._pos = o.pos || null;
            self._pannerAttr = {
                coneInnerAngle: typeof o.coneInnerAngle !== 'undefined' ? o.coneInnerAngle : 360,
                coneOuterAngle: typeof o.coneOuterAngle !== 'undefined' ? o.coneOuterAngle : 360,
                coneOuterGain: typeof o.coneOuterGain !== 'undefined' ? o.coneOuterGain : 0,
                distanceModel: typeof o.distanceModel !== 'undefined' ? o.distanceModel : 'inverse',
                maxDistance: typeof o.maxDistance !== 'undefined' ? o.maxDistance : 10000,
                panningModel: typeof o.panningModel !== 'undefined' ? o.panningModel : 'HRTF',
                refDistance: typeof o.refDistance !== 'undefined' ? o.refDistance : 1,
                rolloffFactor: typeof o.rolloffFactor !== 'undefined' ? o.rolloffFactor : 1
            };
            // Setup event listeners.
            self._onstereo = o.onstereo ? [
                {
                    fn: o.onstereo
                }
            ] : [];
            self._onpos = o.onpos ? [
                {
                    fn: o.onpos
                }
            ] : [];
            self._onorientation = o.onorientation ? [
                {
                    fn: o.onorientation
                }
            ] : [];
            // Complete initilization with howler.js core's init function.
            return _super.call(this, o);
        };
    }(Howl.prototype.init);
    /**
   * Get/set the stereo panning of the audio source for this sound or all in the group.
   * @param  {Number} pan  A value of -1.0 is all the way left and 1.0 is all the way right.
   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.
   * @return {Howl/Number}    Returns self or the current stereo panning value.
   */ Howl.prototype.stereo = function(pan, id) {
        var self = this;
        // Stop right here if not using Web Audio.
        if (!self._webAudio) {
            return self;
        }
        // If the sound hasn't loaded, add it to the load queue to change stereo pan when capable.
        if (self._state !== 'loaded') {
            self._queue.push({
                event: 'stereo',
                action: function action() {
                    self.stereo(pan, id);
                }
            });
            return self;
        }
        // Check for PannerStereoNode support and fallback to PannerNode if it doesn't exist.
        var pannerType = typeof Howler.ctx.createStereoPanner === 'undefined' ? 'spatial' : 'stereo';
        // Setup the group's stereo panning if no ID is passed.
        if (typeof id === 'undefined') {
            // Return the group's stereo panning if no parameters are passed.
            if (typeof pan === 'number') {
                self._stereo = pan;
                self._pos = [
                    pan,
                    0,
                    0
                ];
            } else {
                return self._stereo;
            }
        }
        // Change the streo panning of one or all sounds in group.
        var ids = self._getSoundIds(id);
        for(var i = 0; i < ids.length; i++){
            // Get the sound.
            var sound = self._soundById(ids[i]);
            if (sound) {
                if (typeof pan === 'number') {
                    sound._stereo = pan;
                    sound._pos = [
                        pan,
                        0,
                        0
                    ];
                    if (sound._node) {
                        // If we are falling back, make sure the panningModel is equalpower.
                        sound._pannerAttr.panningModel = 'equalpower';
                        // Check if there is a panner setup and create a new one if not.
                        if (!sound._panner || !sound._panner.pan) {
                            setupPanner(sound, pannerType);
                        }
                        if (pannerType === 'spatial') {
                            if (typeof sound._panner.positionX !== 'undefined') {
                                sound._panner.positionX.setValueAtTime(pan, Howler.ctx.currentTime);
                                sound._panner.positionY.setValueAtTime(0, Howler.ctx.currentTime);
                                sound._panner.positionZ.setValueAtTime(0, Howler.ctx.currentTime);
                            } else {
                                sound._panner.setPosition(pan, 0, 0);
                            }
                        } else {
                            sound._panner.pan.setValueAtTime(pan, Howler.ctx.currentTime);
                        }
                    }
                    self._emit('stereo', sound._id);
                } else {
                    return sound._stereo;
                }
            }
        }
        return self;
    };
    /**
   * Get/set the 3D spatial position of the audio source for this sound or group relative to the global listener.
   * @param  {Number} x  The x-position of the audio source.
   * @param  {Number} y  The y-position of the audio source.
   * @param  {Number} z  The z-position of the audio source.
   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.
   * @return {Howl/Array}    Returns self or the current 3D spatial position: [x, y, z].
   */ Howl.prototype.pos = function(x, y, z, id) {
        var self = this;
        // Stop right here if not using Web Audio.
        if (!self._webAudio) {
            return self;
        }
        // If the sound hasn't loaded, add it to the load queue to change position when capable.
        if (self._state !== 'loaded') {
            self._queue.push({
                event: 'pos',
                action: function action() {
                    self.pos(x, y, z, id);
                }
            });
            return self;
        }
        // Set the defaults for optional 'y' & 'z'.
        y = typeof y !== 'number' ? 0 : y;
        z = typeof z !== 'number' ? -0.5 : z;
        // Setup the group's spatial position if no ID is passed.
        if (typeof id === 'undefined') {
            // Return the group's spatial position if no parameters are passed.
            if (typeof x === 'number') {
                self._pos = [
                    x,
                    y,
                    z
                ];
            } else {
                return self._pos;
            }
        }
        // Change the spatial position of one or all sounds in group.
        var ids = self._getSoundIds(id);
        for(var i = 0; i < ids.length; i++){
            // Get the sound.
            var sound = self._soundById(ids[i]);
            if (sound) {
                if (typeof x === 'number') {
                    sound._pos = [
                        x,
                        y,
                        z
                    ];
                    if (sound._node) {
                        // Check if there is a panner setup and create a new one if not.
                        if (!sound._panner || sound._panner.pan) {
                            setupPanner(sound, 'spatial');
                        }
                        if (typeof sound._panner.positionX !== 'undefined') {
                            sound._panner.positionX.setValueAtTime(x, Howler.ctx.currentTime);
                            sound._panner.positionY.setValueAtTime(y, Howler.ctx.currentTime);
                            sound._panner.positionZ.setValueAtTime(z, Howler.ctx.currentTime);
                        } else {
                            sound._panner.setPosition(x, y, z);
                        }
                    }
                    self._emit('pos', sound._id);
                } else {
                    return sound._pos;
                }
            }
        }
        return self;
    };
    /**
   * Get/set the direction the audio source is pointing in the 3D cartesian coordinate
   * space. Depending on how direction the sound is, based on the `cone` attributes,
   * a sound pointing away from the listener can be quiet or silent.
   * @param  {Number} x  The x-orientation of the source.
   * @param  {Number} y  The y-orientation of the source.
   * @param  {Number} z  The z-orientation of the source.
   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.
   * @return {Howl/Array}    Returns self or the current 3D spatial orientation: [x, y, z].
   */ Howl.prototype.orientation = function(x, y, z, id) {
        var self = this;
        // Stop right here if not using Web Audio.
        if (!self._webAudio) {
            return self;
        }
        // If the sound hasn't loaded, add it to the load queue to change orientation when capable.
        if (self._state !== 'loaded') {
            self._queue.push({
                event: 'orientation',
                action: function action() {
                    self.orientation(x, y, z, id);
                }
            });
            return self;
        }
        // Set the defaults for optional 'y' & 'z'.
        y = typeof y !== 'number' ? self._orientation[1] : y;
        z = typeof z !== 'number' ? self._orientation[2] : z;
        // Setup the group's spatial orientation if no ID is passed.
        if (typeof id === 'undefined') {
            // Return the group's spatial orientation if no parameters are passed.
            if (typeof x === 'number') {
                self._orientation = [
                    x,
                    y,
                    z
                ];
            } else {
                return self._orientation;
            }
        }
        // Change the spatial orientation of one or all sounds in group.
        var ids = self._getSoundIds(id);
        for(var i = 0; i < ids.length; i++){
            // Get the sound.
            var sound = self._soundById(ids[i]);
            if (sound) {
                if (typeof x === 'number') {
                    sound._orientation = [
                        x,
                        y,
                        z
                    ];
                    if (sound._node) {
                        // Check if there is a panner setup and create a new one if not.
                        if (!sound._panner) {
                            // Make sure we have a position to setup the node with.
                            if (!sound._pos) {
                                sound._pos = self._pos || [
                                    0,
                                    0,
                                    -0.5
                                ];
                            }
                            setupPanner(sound, 'spatial');
                        }
                        if (typeof sound._panner.orientationX !== 'undefined') {
                            sound._panner.orientationX.setValueAtTime(x, Howler.ctx.currentTime);
                            sound._panner.orientationY.setValueAtTime(y, Howler.ctx.currentTime);
                            sound._panner.orientationZ.setValueAtTime(z, Howler.ctx.currentTime);
                        } else {
                            sound._panner.setOrientation(x, y, z);
                        }
                    }
                    self._emit('orientation', sound._id);
                } else {
                    return sound._orientation;
                }
            }
        }
        return self;
    };
    /**
   * Get/set the panner node's attributes for a sound or group of sounds.
   * This method can optionall take 0, 1 or 2 arguments.
   *   pannerAttr() -> Returns the group's values.
   *   pannerAttr(id) -> Returns the sound id's values.
   *   pannerAttr(o) -> Set's the values of all sounds in this Howl group.
   *   pannerAttr(o, id) -> Set's the values of passed sound id.
   *
   *   Attributes:
   *     coneInnerAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,
   *                      inside of which there will be no volume reduction.
   *     coneOuterAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,
   *                      outside of which the volume will be reduced to a constant value of `coneOuterGain`.
   *     coneOuterGain - (0 by default) A parameter for directional audio sources, this is the gain outside of the
   *                     `coneOuterAngle`. It is a linear value in the range `[0, 1]`.
   *     distanceModel - ('inverse' by default) Determines algorithm used to reduce volume as audio moves away from
   *                     listener. Can be `linear`, `inverse` or `exponential.
   *     maxDistance - (10000 by default) The maximum distance between source and listener, after which the volume
   *                   will not be reduced any further.
   *     refDistance - (1 by default) A reference distance for reducing volume as source moves further from the listener.
   *                   This is simply a variable of the distance model and has a different effect depending on which model
   *                   is used and the scale of your coordinates. Generally, volume will be equal to 1 at this distance.
   *     rolloffFactor - (1 by default) How quickly the volume reduces as source moves from listener. This is simply a
   *                     variable of the distance model and can be in the range of `[0, 1]` with `linear` and `[0, ∞]`
   *                     with `inverse` and `exponential`.
   *     panningModel - ('HRTF' by default) Determines which spatialization algorithm is used to position audio.
   *                     Can be `HRTF` or `equalpower`.
   *
   * @return {Howl/Object} Returns self or current panner attributes.
   */ Howl.prototype.pannerAttr = function() {
        var self = this;
        var args = arguments;
        var o, id, sound;
        // Stop right here if not using Web Audio.
        if (!self._webAudio) {
            return self;
        }
        // Determine the values based on arguments.
        if (args.length === 0) {
            // Return the group's panner attribute values.
            return self._pannerAttr;
        } else if (args.length === 1) {
            if (_type_of(args[0]) === 'object') {
                o = args[0];
                // Set the grou's panner attribute values.
                if (typeof id === 'undefined') {
                    if (!o.pannerAttr) {
                        o.pannerAttr = {
                            coneInnerAngle: o.coneInnerAngle,
                            coneOuterAngle: o.coneOuterAngle,
                            coneOuterGain: o.coneOuterGain,
                            distanceModel: o.distanceModel,
                            maxDistance: o.maxDistance,
                            refDistance: o.refDistance,
                            rolloffFactor: o.rolloffFactor,
                            panningModel: o.panningModel
                        };
                    }
                    self._pannerAttr = {
                        coneInnerAngle: typeof o.pannerAttr.coneInnerAngle !== 'undefined' ? o.pannerAttr.coneInnerAngle : self._coneInnerAngle,
                        coneOuterAngle: typeof o.pannerAttr.coneOuterAngle !== 'undefined' ? o.pannerAttr.coneOuterAngle : self._coneOuterAngle,
                        coneOuterGain: typeof o.pannerAttr.coneOuterGain !== 'undefined' ? o.pannerAttr.coneOuterGain : self._coneOuterGain,
                        distanceModel: typeof o.pannerAttr.distanceModel !== 'undefined' ? o.pannerAttr.distanceModel : self._distanceModel,
                        maxDistance: typeof o.pannerAttr.maxDistance !== 'undefined' ? o.pannerAttr.maxDistance : self._maxDistance,
                        refDistance: typeof o.pannerAttr.refDistance !== 'undefined' ? o.pannerAttr.refDistance : self._refDistance,
                        rolloffFactor: typeof o.pannerAttr.rolloffFactor !== 'undefined' ? o.pannerAttr.rolloffFactor : self._rolloffFactor,
                        panningModel: typeof o.pannerAttr.panningModel !== 'undefined' ? o.pannerAttr.panningModel : self._panningModel
                    };
                }
            } else {
                // Return this sound's panner attribute values.
                sound = self._soundById(parseInt(args[0], 10));
                return sound ? sound._pannerAttr : self._pannerAttr;
            }
        } else if (args.length === 2) {
            o = args[0];
            id = parseInt(args[1], 10);
        }
        // Update the values of the specified sounds.
        var ids = self._getSoundIds(id);
        for(var i = 0; i < ids.length; i++){
            sound = self._soundById(ids[i]);
            if (sound) {
                // Merge the new values into the sound.
                var pa = sound._pannerAttr;
                pa = {
                    coneInnerAngle: typeof o.coneInnerAngle !== 'undefined' ? o.coneInnerAngle : pa.coneInnerAngle,
                    coneOuterAngle: typeof o.coneOuterAngle !== 'undefined' ? o.coneOuterAngle : pa.coneOuterAngle,
                    coneOuterGain: typeof o.coneOuterGain !== 'undefined' ? o.coneOuterGain : pa.coneOuterGain,
                    distanceModel: typeof o.distanceModel !== 'undefined' ? o.distanceModel : pa.distanceModel,
                    maxDistance: typeof o.maxDistance !== 'undefined' ? o.maxDistance : pa.maxDistance,
                    refDistance: typeof o.refDistance !== 'undefined' ? o.refDistance : pa.refDistance,
                    rolloffFactor: typeof o.rolloffFactor !== 'undefined' ? o.rolloffFactor : pa.rolloffFactor,
                    panningModel: typeof o.panningModel !== 'undefined' ? o.panningModel : pa.panningModel
                };
                // Update the panner values or create a new panner if none exists.
                var panner = sound._panner;
                if (panner) {
                    panner.coneInnerAngle = pa.coneInnerAngle;
                    panner.coneOuterAngle = pa.coneOuterAngle;
                    panner.coneOuterGain = pa.coneOuterGain;
                    panner.distanceModel = pa.distanceModel;
                    panner.maxDistance = pa.maxDistance;
                    panner.refDistance = pa.refDistance;
                    panner.rolloffFactor = pa.rolloffFactor;
                    panner.panningModel = pa.panningModel;
                } else {
                    // Make sure we have a position to setup the node with.
                    if (!sound._pos) {
                        sound._pos = self._pos || [
                            0,
                            0,
                            -0.5
                        ];
                    }
                    // Create a new panner node.
                    setupPanner(sound, 'spatial');
                }
            }
        }
        return self;
    };
    /** Single Sound Methods **/ /***************************************************************************/ /**
   * Add new properties to the core Sound init.
   * @param  {Function} _super Core Sound init method.
   * @return {Sound}
   */ Sound.prototype.init = function(_super) {
        return function() {
            var self = this;
            var parent = self._parent;
            // Setup user-defined default properties.
            self._orientation = parent._orientation;
            self._stereo = parent._stereo;
            self._pos = parent._pos;
            self._pannerAttr = parent._pannerAttr;
            // Complete initilization with howler.js core Sound's init function.
            _super.call(this);
            // If a stereo or position was specified, set it up.
            if (self._stereo) {
                parent.stereo(self._stereo);
            } else if (self._pos) {
                parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);
            }
        };
    }(Sound.prototype.init);
    /**
   * Override the Sound.reset method to clean up properties from the spatial plugin.
   * @param  {Function} _super Sound reset method.
   * @return {Sound}
   */ Sound.prototype.reset = function(_super) {
        return function() {
            var self = this;
            var parent = self._parent;
            // Reset all spatial plugin properties on this sound.
            self._orientation = parent._orientation;
            self._stereo = parent._stereo;
            self._pos = parent._pos;
            self._pannerAttr = parent._pannerAttr;
            // If a stereo or position was specified, set it up.
            if (self._stereo) {
                parent.stereo(self._stereo);
            } else if (self._pos) {
                parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);
            } else if (self._panner) {
                // Disconnect the panner.
                self._panner.disconnect(0);
                self._panner = undefined;
                parent._refreshBuffer(self);
            }
            // Complete resetting of the sound.
            return _super.call(this);
        };
    }(Sound.prototype.reset);
    /** Helper Methods **/ /***************************************************************************/ /**
   * Create a new panner node and save it on the sound.
   * @param  {Sound} sound Specific sound to setup panning on.
   * @param {String} type Type of panner to create: 'stereo' or 'spatial'.
   */ var setupPanner = function setupPanner(sound, type) {
        type = type || 'spatial';
        // Create the new panner node.
        if (type === 'spatial') {
            sound._panner = Howler.ctx.createPanner();
            sound._panner.coneInnerAngle = sound._pannerAttr.coneInnerAngle;
            sound._panner.coneOuterAngle = sound._pannerAttr.coneOuterAngle;
            sound._panner.coneOuterGain = sound._pannerAttr.coneOuterGain;
            sound._panner.distanceModel = sound._pannerAttr.distanceModel;
            sound._panner.maxDistance = sound._pannerAttr.maxDistance;
            sound._panner.refDistance = sound._pannerAttr.refDistance;
            sound._panner.rolloffFactor = sound._pannerAttr.rolloffFactor;
            sound._panner.panningModel = sound._pannerAttr.panningModel;
            if (typeof sound._panner.positionX !== 'undefined') {
                sound._panner.positionX.setValueAtTime(sound._pos[0], Howler.ctx.currentTime);
                sound._panner.positionY.setValueAtTime(sound._pos[1], Howler.ctx.currentTime);
                sound._panner.positionZ.setValueAtTime(sound._pos[2], Howler.ctx.currentTime);
            } else {
                sound._panner.setPosition(sound._pos[0], sound._pos[1], sound._pos[2]);
            }
            if (typeof sound._panner.orientationX !== 'undefined') {
                sound._panner.orientationX.setValueAtTime(sound._orientation[0], Howler.ctx.currentTime);
                sound._panner.orientationY.setValueAtTime(sound._orientation[1], Howler.ctx.currentTime);
                sound._panner.orientationZ.setValueAtTime(sound._orientation[2], Howler.ctx.currentTime);
            } else {
                sound._panner.setOrientation(sound._orientation[0], sound._orientation[1], sound._orientation[2]);
            }
        } else {
            sound._panner = Howler.ctx.createStereoPanner();
            sound._panner.pan.setValueAtTime(sound._stereo, Howler.ctx.currentTime);
        }
        sound._panner.connect(sound._node);
        // Update the connections.
        if (!sound._paused) {
            sound._parent.pause(sound._id, true).play(sound._id, true);
        }
    };
})();

/** https://github.com/taylorhakes/promise-polyfill */ function _instanceof(left, right) {
    if (right != null && typeof Symbol !== "undefined" && right[Symbol.hasInstance]) {
        return !!right[Symbol.hasInstance](left);
    } else {
        return left instanceof right;
    }
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
!function(e, n) {
    (typeof exports === "undefined" ? "undefined" : _type_of(exports)) == 'object' && typeof module != 'undefined' ? n() : typeof define == 'function' && define.amd ? define(n) : n();
}(0, function() {
    'use strict';
    function e(e) {
        var n = this.constructor;
        return this.then(function(t) {
            return n.resolve(e()).then(function() {
                return t;
            });
        }, function(t) {
            return n.resolve(e()).then(function() {
                return n.reject(t);
            });
        });
    }
    function n() {}
    function t(e) {
        if (!_instanceof(this, t)) throw new TypeError('Promises must be constructed via new');
        if (typeof e != 'function') throw new TypeError('not a function');
        this._state = 0, this._handled = !1, this._value = undefined, this._deferreds = [], u(e, this);
    }
    function o(e, n) {
        for(; e._state === 3;)e = e._value;
        e._state !== 0 ? (e._handled = !0, t._immediateFn(function() {
            var _$t = e._state === 1 ? n.onFulfilled : n.onRejected;
            if (_$t !== null) {
                var o;
                try {
                    o = _$t(e._value);
                } catch (f) {
                    return void i(n.promise, f);
                }
                r(n.promise, o);
            } else (e._state === 1 ? r : i)(n.promise, e._value);
        })) : e._deferreds.push(n);
    }
    function r(e, n) {
        try {
            if (n === e) throw new TypeError('A promise cannot be resolved with itself.');
            if (n && ((typeof n === "undefined" ? "undefined" : _type_of(n)) == 'object' || typeof n == 'function')) {
                var o = n.then;
                if (_instanceof(n, t)) return e._state = 3, e._value = n, void f(e);
                if (typeof o == 'function') return void u(function(e, n) {
                    return function() {
                        e.apply(n, arguments);
                    };
                }(o, n), e);
            }
            e._state = 1, e._value = n, f(e);
        } catch (r) {
            i(e, r);
        }
    }
    function i(e, n) {
        e._state = 2, e._value = n, f(e);
    }
    function f(e) {
        e._state === 2 && e._deferreds.length === 0 && t._immediateFn(function() {
            e._handled || t._unhandledRejectionFn(e._value);
        });
        for(var n = 0, r = e._deferreds.length; r > n; n++)o(e, e._deferreds[n]);
        e._deferreds = null;
    }
    function u(e, n) {
        var _$t = !1;
        try {
            e(function(e) {
                _$t || (_$t = !0, r(n, e));
            }, function(e) {
                _$t || (_$t = !0, i(n, e));
            });
        } catch (o) {
            if (_$t) return;
            _$t = !0, i(n, o);
        }
    }
    var c = setTimeout;
    t.prototype.catch = function(e) {
        return this.then(null, e);
    }, t.prototype.then = function(e, t) {
        var r = new this.constructor(n);
        return o(this, new function(e, n, t) {
            this.onFulfilled = typeof e == 'function' ? e : null, this.onRejected = typeof n == 'function' ? n : null, this.promise = t;
        }(e, t, r)), r;
    }, t.prototype.finally = e, t.all = function(e) {
        return new t(function(n, t) {
            function o(e, f) {
                try {
                    if (f && ((typeof f === "undefined" ? "undefined" : _type_of(f)) == 'object' || typeof f == 'function')) {
                        var u = f.then;
                        if (typeof u == 'function') return void u.call(f, function(n) {
                            o(e, n);
                        }, t);
                    }
                    r[e] = f, --i == 0 && n(r);
                } catch (c) {
                    t(c);
                }
            }
            if (!e || typeof e.length == 'undefined') throw new TypeError('Promise.all accepts an array');
            var r = Array.prototype.slice.call(e);
            if (r.length === 0) return n([]);
            for(var i = r.length, f = 0; r.length > f; f++)o(f, r[f]);
        });
    }, t.resolve = function(e) {
        return e && (typeof e === "undefined" ? "undefined" : _type_of(e)) == 'object' && e.constructor === t ? e : new t(function(n) {
            n(e);
        });
    }, t.reject = function(e) {
        return new t(function(n, t) {
            t(e);
        });
    }, t.race = function(e) {
        return new t(function(n, t) {
            for(var o = 0, r = e.length; r > o; o++)e[o].then(n, t);
        });
    }, t._immediateFn = typeof setImmediate == 'function' && function(e) {
        setImmediate(e);
    } || function(e) {
        c(e, 0);
    }, t._unhandledRejectionFn = function(e) {
        void 0 !== console && console && console.warn('Possible Unhandled Promise Rejection:', e);
    };
    var l = function() {
        if (typeof self != 'undefined') return self;
        if (typeof window != 'undefined') return window;
        if (typeof global != 'undefined') return global;
        throw Error('unable to locate global object');
    }();
    'Promise' in l ? l.Promise.prototype.finally || (l.Promise.prototype.finally = e) : l.Promise = t;
});

function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
/** https://github.com/bramstein/fontfaceobserver */ /* Font Face Observer v2.1.0 - © Bram Stein. License: BSD-3-Clause */ (function() {
    function l(a, b) {
        document.addEventListener ? a.addEventListener('scroll', b, !1) : a.attachEvent('scroll', b);
    }
    function m(a) {
        document.body ? a() : document.addEventListener ? document.addEventListener('DOMContentLoaded', function c() {
            document.removeEventListener('DOMContentLoaded', c);
            a();
        }) : document.attachEvent('onreadystatechange', function k() {
            if (document.readyState == 'interactive' || document.readyState == 'complete') document.detachEvent('onreadystatechange', k), a();
        });
    }
    ;
    function t(a) {
        this.a = document.createElement('div');
        this.a.setAttribute('aria-hidden', 'true');
        this.a.appendChild(document.createTextNode(a));
        this.b = document.createElement('span');
        this.c = document.createElement('span');
        this.h = document.createElement('span');
        this.f = document.createElement('span');
        this.g = -1;
        this.b.style.cssText = 'max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;';
        this.c.style.cssText = 'max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;';
        this.f.style.cssText = 'max-width:none;display:inline-block;position:absolute;height:100%;width:100%;overflow:scroll;font-size:16px;';
        this.h.style.cssText = 'display:inline-block;width:200%;height:200%;font-size:16px;max-width:none;';
        this.b.appendChild(this.h);
        this.c.appendChild(this.f);
        this.a.appendChild(this.b);
        this.a.appendChild(this.c);
    }
    function u(a, b) {
        a.a.style.cssText = 'max-width:none;min-width:20px;min-height:20px;display:inline-block;overflow:hidden;position:absolute;width:auto;margin:0;padding:0;top:-999px;white-space:nowrap;font-synthesis:none;font:' + b + ';';
    }
    function z(a) {
        var b = a.a.offsetWidth;
        var c = b + 100;
        a.f.style.width = c + 'px';
        a.c.scrollLeft = c;
        a.b.scrollLeft = a.b.scrollWidth + 100;
        return a.g !== b ? (a.g = b, !0) : !1;
    }
    function A(a, b) {
        function c() {
            var _$a = k;
            z(_$a) && _$a.a.parentNode && b(_$a.g);
        }
        var k = a;
        l(a.b, c);
        l(a.c, c);
        z(a);
    }
    ;
    function B(a, b) {
        var c = b || {};
        this.family = a;
        this.style = c.style || 'normal';
        this.weight = c.weight || 'normal';
        this.stretch = c.stretch || 'normal';
    }
    var C = null;
    var D = null;
    var E = null;
    var F = null;
    function G() {
        if (D === null) if (J() && /Apple/.test(window.navigator.vendor)) {
            var a = /AppleWebKit\/([0-9]+)(?:\.([0-9]+))(?:\.([0-9]+))/.exec(window.navigator.userAgent);
            D = !!a && parseInt(a[1], 10) < 603;
        } else D = !1;
        return D;
    }
    function J() {
        F === null && (F = !!document.fonts);
        return F;
    }
    function K() {
        if (E === null) {
            var a = document.createElement('div');
            try {
                a.style.font = 'condensed 100px sans-serif';
            } catch (b) {}
            E = a.style.font !== '';
        }
        return E;
    }
    function L(a, b) {
        return [
            a.style,
            a.weight,
            K() ? a.stretch : '',
            '100px',
            b
        ].join(' ');
    }
    B.prototype.load = function(a, b) {
        var c = this;
        var k = a || 'BESbswy';
        var r = 0;
        var n = b || 3E3;
        var H = new Date().getTime();
        return new Promise(function(a, b) {
            if (J() && !G()) {
                var M = new Promise(function(a, b) {
                    function e() {
                        new Date().getTime() - H >= n ? b(Error('' + n + 'ms timeout exceeded')) : document.fonts.load(L(c, '"' + c.family + '"'), k).then(function(c) {
                            c.length >= 1 ? a() : setTimeout(e, 25);
                        }, b);
                    }
                    e();
                });
                var N = new Promise(function(a, c) {
                    r = setTimeout(function() {
                        c(Error('' + n + 'ms timeout exceeded'));
                    }, n);
                });
                Promise.race([
                    N,
                    M
                ]).then(function() {
                    clearTimeout(r);
                    a(c);
                }, b);
            } else {
                m(function() {
                    function v() {
                        var _$b;
                        if (_$b = f != -1 && g != -1 || f != -1 && h != -1 || g != -1 && h != -1) (_$b = f != g && f != h && g != h) || (C === null && (_$b = /AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent), C = !!_$b && (parseInt(_$b[1], 10) < 536 || parseInt(_$b[1], 10) === 536 && parseInt(_$b[2], 10) <= 11)), _$b = C && (f == w && g == w && h == w || f == x && g == x && h == x || f == y && g == y && h == y)), _$b = !_$b;
                        _$b && (d.parentNode && d.parentNode.removeChild(d), clearTimeout(r), a(c));
                    }
                    function I() {
                        if (new Date().getTime() - H >= n) {
                            d.parentNode && d.parentNode.removeChild(d), b(Error('' + n + 'ms timeout exceeded'));
                        } else {
                            var _$a = document.hidden;
                            if (!0 === _$a || void 0 === _$a) f = e.a.offsetWidth, g = p.a.offsetWidth, h = q.a.offsetWidth, v();
                            r = setTimeout(I, 50);
                        }
                    }
                    var e = new t(k);
                    var p = new t(k);
                    var q = new t(k);
                    var f = -1;
                    var g = -1;
                    var h = -1;
                    var w = -1;
                    var x = -1;
                    var y = -1;
                    var d = document.createElement('div');
                    d.dir = 'ltr';
                    u(e, L(c, 'sans-serif'));
                    u(p, L(c, 'serif'));
                    u(q, L(c, 'monospace'));
                    d.appendChild(e.a);
                    d.appendChild(p.a);
                    d.appendChild(q.a);
                    document.body.appendChild(d);
                    w = e.a.offsetWidth;
                    x = p.a.offsetWidth;
                    y = q.a.offsetWidth;
                    I();
                    A(e, function(a) {
                        f = a;
                        v();
                    });
                    u(e, L(c, '"' + c.family + '",sans-serif'));
                    A(p, function(a) {
                        g = a;
                        v();
                    });
                    u(p, L(c, '"' + c.family + '",serif'));
                    A(q, function(a) {
                        h = a;
                        v();
                    });
                    u(q, L(c, '"' + c.family + '",monospace'));
                });
            }
        });
    };
    (typeof module === "undefined" ? "undefined" : _type_of(module)) === 'object' ? module.exports = B : (window.FontFaceObserver = B, window.FontFaceObserver.prototype.load = B.prototype.load);
})();

